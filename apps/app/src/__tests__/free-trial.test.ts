import { describe, expect, it, beforeEach, afterEach } from "@jest/globals"
import { addDays, isBefore } from "date-fns"

import { FREE_TRIAL_REMINDER_DAYS_BEFORE } from "@/constants"
import { prisma } from "@/lib/prisma"
import { BillingPeriod, SubscriptionStatus } from "@prisma/client"

// Mock data
const mockUser = {
  email: "<EMAIL>",
  username: "testuser",
  hasUsedFreeTrial: false,
  mangopayUserId: "test-mangopay-user",
  mangopayWalletId: "test-mangopay-wallet",
}

const mockPlan = {
  name: "Test Plan with Free Trial",
  description: "Test plan with 7-day free trial",
  monthlyPrice: 1000, // €10.00 in cents
  annualPrice: 10000, // €100.00 in cents
  freeTrialDays: 7,
  features: ["Feature 1", "Feature 2"],
  isRecommended: false,
  isActive: true,
}

describe("Free Trial System", () => {
  let testUser: any
  let testPlan: any

  beforeEach(async () => {
    // Create test user
    testUser = await prisma.user.create({
      data: mockUser,
    })

    // Create test plan with free trial
    testPlan = await prisma.plan.create({
      data: mockPlan,
    })
  })

  afterEach(async () => {
    // Clean up test data
    await prisma.subscription.deleteMany({
      where: { userId: testUser.id },
    })
    await prisma.user.delete({
      where: { id: testUser.id },
    })
    await prisma.plan.delete({
      where: { id: testPlan.id },
    })
  })

  describe("Free Trial Eligibility", () => {
    it("should allow free trial for users who haven't used it", async () => {
      const user = await prisma.user.findUnique({
        where: { id: testUser.id },
      })

      expect(user?.hasUsedFreeTrial).toBe(false)
    })

    it("should prevent multiple free trials for the same user", async () => {
      // Mark user as having used free trial
      await prisma.user.update({
        where: { id: testUser.id },
        data: { hasUsedFreeTrial: true },
      })

      const user = await prisma.user.findUnique({
        where: { id: testUser.id },
      })

      expect(user?.hasUsedFreeTrial).toBe(true)
    })

    it("should check if plan offers free trial", async () => {
      const plan = await prisma.plan.findUnique({
        where: { id: testPlan.id },
      })

      expect(plan?.freeTrialDays).toBe(7)
      expect(plan?.freeTrialDays).toBeGreaterThan(0)
    })
  })

  describe("Free Trial Subscription Creation", () => {
    it("should create subscription with correct free trial dates", async () => {
      const startDate = new Date()
      const expectedTrialEndDate = addDays(startDate, testPlan.freeTrialDays)
      const expectedEndDate = addDays(expectedTrialEndDate, 30) // Monthly subscription

      const subscription = await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate,
          endDate: expectedEndDate,
          isFreeTrial: true,
          freeTrialEndDate: expectedTrialEndDate,
          trialReminderSent: false,
        },
      })

      expect(subscription.isFreeTrial).toBe(true)
      expect(subscription.freeTrialEndDate).toBeDefined()
      expect(subscription.trialReminderSent).toBe(false)
      
      // Check that trial end date is correct
      const daysDifference = Math.round(
        (subscription.freeTrialEndDate!.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
      )
      expect(daysDifference).toBe(testPlan.freeTrialDays)
    })

    it("should mark user as having used free trial", async () => {
      // Simulate free trial subscription creation
      await prisma.user.update({
        where: { id: testUser.id },
        data: { hasUsedFreeTrial: true },
      })

      const user = await prisma.user.findUnique({
        where: { id: testUser.id },
      })

      expect(user?.hasUsedFreeTrial).toBe(true)
    })
  })

  describe("Free Trial Reminder Logic", () => {
    it("should identify trials needing reminders", async () => {
      const now = new Date()
      const trialEndDate = addDays(now, FREE_TRIAL_REMINDER_DAYS_BEFORE - 1) // Should trigger reminder

      const subscription = await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate: now,
          endDate: addDays(trialEndDate, 30),
          isFreeTrial: true,
          freeTrialEndDate: trialEndDate,
          trialReminderSent: false,
        },
      })

      // Check if this subscription should receive a reminder
      const reminderDate = addDays(now, FREE_TRIAL_REMINDER_DAYS_BEFORE)
      const shouldSendReminder = subscription.freeTrialEndDate! <= reminderDate
      
      expect(shouldSendReminder).toBe(true)
      expect(subscription.trialReminderSent).toBe(false)
    })

    it("should not send duplicate reminders", async () => {
      const now = new Date()
      const trialEndDate = addDays(now, FREE_TRIAL_REMINDER_DAYS_BEFORE - 1)

      const subscription = await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate: now,
          endDate: addDays(trialEndDate, 30),
          isFreeTrial: true,
          freeTrialEndDate: trialEndDate,
          trialReminderSent: true, // Already sent
        },
      })

      expect(subscription.trialReminderSent).toBe(true)
    })
  })

  describe("Free Trial Expiration Logic", () => {
    it("should identify expired trials", async () => {
      const now = new Date()
      const expiredTrialEndDate = addDays(now, -1) // Yesterday

      const subscription = await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate: addDays(now, -8),
          endDate: addDays(now, 22),
          isFreeTrial: true,
          freeTrialEndDate: expiredTrialEndDate,
          trialReminderSent: true,
        },
      })

      const isExpired = isBefore(subscription.freeTrialEndDate!, now)
      expect(isExpired).toBe(true)
    })

    it("should transition from trial to regular subscription", async () => {
      const subscription = await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate: new Date(),
          endDate: addDays(new Date(), 30),
          isFreeTrial: true,
          freeTrialEndDate: addDays(new Date(), 7),
          trialReminderSent: false,
        },
      })

      // Simulate trial completion
      const updatedSubscription = await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          isFreeTrial: false,
          freeTrialEndDate: null,
        },
      })

      expect(updatedSubscription.isFreeTrial).toBe(false)
      expect(updatedSubscription.freeTrialEndDate).toBeNull()
    })
  })

  describe("Free Trial Payment Logic", () => {
    it("should handle €0 initial payment for trial", async () => {
      // This would typically be tested with the actual payment processing
      // For now, we'll just verify the subscription structure supports it
      const subscription = await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate: new Date(),
          endDate: addDays(new Date(), 30),
          isFreeTrial: true,
          freeTrialEndDate: addDays(new Date(), 7),
          trialReminderSent: false,
        },
      })

      // Create initial €0 payment
      const initialPayment = await prisma.payment.create({
        data: {
          subscriptionId: subscription.id,
          amount: 0, // €0 for trial validation
          currency: "EUR",
          status: "SUCCEEDED",
          mangopayPayinId: null,
        },
      })

      expect(initialPayment.amount).toBe(0)
      expect(initialPayment.status).toBe("SUCCEEDED")
    })
  })
})
