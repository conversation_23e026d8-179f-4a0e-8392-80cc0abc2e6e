import { describe, expect, it, beforeEach, afterEach } from "@jest/globals"

import { prisma } from "@/lib/prisma"
import { BillingPeriod } from "@prisma/client"

// Mock data
const mockPlan = {
  name: "Test Plan",
  description: "Test plan for promotional codes",
  monthlyPrice: 1000, // €10.00 in cents
  annualPrice: 10000, // €100.00 in cents
  features: ["Feature 1", "Feature 2"],
  isRecommended: false,
  isActive: true,
}

const mockPromotionalCode = {
  code: "TEST2024",
  name: "Test Promotional Code",
  description: "Test code for unit tests",
  effectType: "BOTH" as const,
  discountType: "PERCENTAGE" as const,
  discountValue: 20, // 20% discount
  paymentCount: 1,
  isActive: true,
}

describe("Promotional Codes", () => {
  let testPlan: any
  let testPromotionalCode: any

  beforeEach(async () => {
    // Create test plan
    testPlan = await prisma.plan.create({
      data: mockPlan,
    })

    // Create test promotional code
    testPromotionalCode = await prisma.promotionalCode.create({
      data: {
        ...mockPromotionalCode,
        restrictedToPlans: {
          connect: { id: testPlan.id },
        },
      },
    })
  })

  afterEach(async () => {
    // Clean up test data
    await prisma.promotionalCodeUsage.deleteMany({
      where: { promotionalCodeId: testPromotionalCode.id },
    })
    await prisma.promotionalCode.delete({
      where: { id: testPromotionalCode.id },
    })
    await prisma.plan.delete({
      where: { id: testPlan.id },
    })
  })

  describe("Promotional Code Creation", () => {
    it("should create a promotional code with valid data", async () => {
      const code = await prisma.promotionalCode.findUnique({
        where: { id: testPromotionalCode.id },
        include: { restrictedToPlans: true },
      })

      expect(code).toBeDefined()
      expect(code?.code).toBe("TEST2024")
      expect(code?.discountValue).toBe(20)
      expect(code?.restrictedToPlans).toHaveLength(1)
      expect(code?.restrictedToPlans[0].id).toBe(testPlan.id)
    })

    it("should enforce unique code constraint", async () => {
      await expect(
        prisma.promotionalCode.create({
          data: {
            ...mockPromotionalCode,
            name: "Duplicate Code Test",
          },
        })
      ).rejects.toThrow()
    })
  })

  describe("Promotional Code Validation", () => {
    it("should validate active promotional code for correct plan and billing period", async () => {
      const code = await prisma.promotionalCode.findUnique({
        where: { code: "TEST2024" },
        include: { restrictedToPlans: true },
      })

      expect(code).toBeDefined()
      expect(code?.isActive).toBe(true)
      expect(code?.effectType).toBe("BOTH")
      
      // Check if plan is in restricted plans
      const isValidForPlan = code?.restrictedToPlans.some(plan => plan.id === testPlan.id)
      expect(isValidForPlan).toBe(true)
    })

    it("should reject inactive promotional codes", async () => {
      // Deactivate the code
      await prisma.promotionalCode.update({
        where: { id: testPromotionalCode.id },
        data: { isActive: false },
      })

      const code = await prisma.promotionalCode.findUnique({
        where: { code: "TEST2024" },
      })

      expect(code?.isActive).toBe(false)
    })

    it("should respect effect type restrictions", async () => {
      // Create a monthly-only code
      const monthlyCode = await prisma.promotionalCode.create({
        data: {
          ...mockPromotionalCode,
          code: "MONTHLY2024",
          effectType: "MONTHLY_ONLY",
        },
      })

      expect(monthlyCode.effectType).toBe("MONTHLY_ONLY")

      // Clean up
      await prisma.promotionalCode.delete({
        where: { id: monthlyCode.id },
      })
    })
  })

  describe("Promotional Code Usage Tracking", () => {
    it("should track usage count correctly", async () => {
      const initialCode = await prisma.promotionalCode.findUnique({
        where: { id: testPromotionalCode.id },
      })
      expect(initialCode?.currentUsageCount).toBe(0)

      // Simulate usage
      await prisma.promotionalCode.update({
        where: { id: testPromotionalCode.id },
        data: {
          currentUsageCount: {
            increment: 1,
          },
        },
      })

      const updatedCode = await prisma.promotionalCode.findUnique({
        where: { id: testPromotionalCode.id },
      })
      expect(updatedCode?.currentUsageCount).toBe(1)
    })

    it("should enforce maximum usage limit", async () => {
      // Set max usage to 1
      await prisma.promotionalCode.update({
        where: { id: testPromotionalCode.id },
        data: { maxUsageCount: 1 },
      })

      // Use the code once
      await prisma.promotionalCode.update({
        where: { id: testPromotionalCode.id },
        data: {
          currentUsageCount: {
            increment: 1,
          },
        },
      })

      const code = await prisma.promotionalCode.findUnique({
        where: { id: testPromotionalCode.id },
      })

      expect(code?.currentUsageCount).toBe(1)
      expect(code?.maxUsageCount).toBe(1)
      // Code should be at its usage limit
      expect(code?.currentUsageCount).toBeGreaterThanOrEqual(code?.maxUsageCount!)
    })
  })

  describe("Discount Calculations", () => {
    it("should calculate percentage discounts correctly", () => {
      const originalPrice = 1000 // €10.00 in cents
      const discountPercentage = 20
      const expectedDiscountedPrice = originalPrice - (originalPrice * discountPercentage) / 100
      
      expect(expectedDiscountedPrice).toBe(800) // €8.00 in cents
    })

    it("should calculate fixed amount discounts correctly", () => {
      const originalPrice = 1000 // €10.00 in cents
      const discountAmount = 200 // €2.00 in cents
      const expectedDiscountedPrice = Math.max(0, originalPrice - discountAmount)
      
      expect(expectedDiscountedPrice).toBe(800) // €8.00 in cents
    })

    it("should not allow negative prices with fixed discounts", () => {
      const originalPrice = 500 // €5.00 in cents
      const discountAmount = 1000 // €10.00 in cents
      const expectedDiscountedPrice = Math.max(0, originalPrice - discountAmount)
      
      expect(expectedDiscountedPrice).toBe(0)
    })
  })
})
