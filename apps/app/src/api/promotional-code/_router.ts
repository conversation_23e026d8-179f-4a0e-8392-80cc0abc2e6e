import { adminAuthenticatedProcedure, authenticatedProcedure, publicProcedure, router } from "@/lib/server/trpc"

import { createPromotionalCode, deletePromotionalCode, updatePromotionalCode } from "./mutations"
import { getPromotionalCode, listPromotionalCodes, validatePromotionalCode } from "./queries"
import {
  createPromotionalCodeSchemaWithValidation,
  deletePromotionalCodeSchema,
  getPromotionalCodeSchema,
  listPromotionalCodesSchema,
  promotionalCodeListResponseSchema,
  promotionalCodeResponseSchema,
  promotionalCodeValidationResponseSchema,
  updatePromotionalCodeSchemaWithValidation,
  validatePromotionalCodeSchema,
} from "./schemas"

export const promotionalCodeRouter = router({
  // Admin procedures
  create: adminAuthenticatedProcedure
    .input(createPromotionalCodeSchemaWithValidation)
    .output(promotionalCodeResponseSchema)
    .mutation(createPromotionalCode),

  update: adminAuthenticatedProcedure
    .input(updatePromotionalCodeSchemaWithValidation)
    .output(promotionalCodeResponseSchema)
    .mutation(updatePromotionalCode),

  delete: adminAuthenticatedProcedure
    .input(deletePromotionalCodeSchema)
    .output({ success: true })
    .mutation(deletePromotionalCode),

  get: adminAuthenticatedProcedure
    .input(getPromotionalCodeSchema)
    .output(promotionalCodeResponseSchema)
    .query(getPromotionalCode),

  list: adminAuthenticatedProcedure
    .input(listPromotionalCodesSchema)
    .output(promotionalCodeListResponseSchema)
    .query(listPromotionalCodes),

  // Public/authenticated procedures
  validate: publicProcedure
    .input(validatePromotionalCodeSchema)
    .output(promotionalCodeValidationResponseSchema)
    .query(validatePromotionalCode),
})
