import { z } from "zod"

import { prisma } from "@/lib/prisma"
import { ApiError, ensureLoggedIn, handleApiError } from "@/lib/utils/server-utils"
import { logger } from "@coheadcoaching/lib"
import { Prisma } from "@prisma/client"

import { promotionalCodeResponseSchema } from "./schemas"

export const createPromotionalCode = async ({ input, ctx: { session } }: { input: any; ctx: { session: any } }) => {
  try {
    ensureLoggedIn(session)

    // Check if user has admin role
    if (!session.user.roles.includes("ADMIN")) {
      return ApiError("unauthorized", "FORBIDDEN")
    }

    const {
      code,
      name,
      description,
      effectType,
      discountType,
      discountValue,
      paymentCount,
      validFrom,
      validUntil,
      maxUsageCount,
      restrictedToPlanIds,
    } = input

    // Validate date range
    if (validFrom && validUntil && validFrom >= validUntil) {
      return ApiError("invalidDateRange")
    }

    // Check if code already exists
    const existingCode = await prisma.promotionalCode.findUnique({
      where: { code: code.toUpperCase() },
    })

    if (existingCode) {
      return ApiError("codeAlreadyExists")
    }

    // Validate restricted plans exist
    if (restrictedToPlanIds && restrictedToPlanIds.length > 0) {
      const plans = await prisma.plan.findMany({
        where: { id: { in: restrictedToPlanIds } },
      })

      if (plans.length !== restrictedToPlanIds.length) {
        return ApiError("invalidPlanIds")
      }
    }

    // Create promotional code
    const promotionalCode = await prisma.promotionalCode.create({
      data: {
        code: code.toUpperCase(),
        name,
        description,
        effectType,
        discountType,
        discountValue,
        paymentCount,
        validFrom,
        validUntil,
        maxUsageCount,
        restrictedToPlans: restrictedToPlanIds
          ? {
              connect: restrictedToPlanIds.map((id: string) => ({ id })),
            }
          : undefined,
      },
      include: {
        restrictedToPlans: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    logger.log("Promotional code created", { codeId: promotionalCode.id, code: promotionalCode.code })

    const data: z.infer<typeof promotionalCodeResponseSchema> = {
      id: promotionalCode.id,
      code: promotionalCode.code,
      name: promotionalCode.name,
      description: promotionalCode.description,
      effectType: promotionalCode.effectType,
      discountType: promotionalCode.discountType,
      discountValue: promotionalCode.discountValue,
      paymentCount: promotionalCode.paymentCount,
      isActive: promotionalCode.isActive,
      validFrom: promotionalCode.validFrom,
      validUntil: promotionalCode.validUntil,
      maxUsageCount: promotionalCode.maxUsageCount,
      currentUsageCount: promotionalCode.currentUsageCount,
      restrictedToPlans: promotionalCode.restrictedToPlans,
      createdAt: promotionalCode.createdAt,
      updatedAt: promotionalCode.updatedAt,
    }

    return data
  } catch (error: unknown) {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === "P2002") {
        return ApiError("codeAlreadyExists")
      }
    }
    return handleApiError(error)
  }
}

export const updatePromotionalCode = async ({ input, ctx: { session } }: { input: any; ctx: { session: any } }) => {
  try {
    ensureLoggedIn(session)

    // Check if user has admin role
    if (!session.user.roles.includes("ADMIN")) {
      return ApiError("unauthorized", "FORBIDDEN")
    }

    const { id, restrictedToPlanIds, ...updateData } = input

    // Check if promotional code exists
    const existingCode = await prisma.promotionalCode.findUnique({
      where: { id },
      include: {
        restrictedToPlans: true,
      },
    })

    if (!existingCode) {
      return ApiError("codeNotFound")
    }

    // Validate date range if both dates are provided
    if (updateData.validFrom && updateData.validUntil && updateData.validFrom >= updateData.validUntil) {
      return ApiError("invalidDateRange")
    }

    // Validate restricted plans exist if provided
    if (restrictedToPlanIds && restrictedToPlanIds.length > 0) {
      const plans = await prisma.plan.findMany({
        where: { id: { in: restrictedToPlanIds } },
      })

      if (plans.length !== restrictedToPlanIds.length) {
        return ApiError("invalidPlanIds")
      }
    }

    // Update promotional code
    const promotionalCode = await prisma.promotionalCode.update({
      where: { id },
      data: {
        ...updateData,
        restrictedToPlans: restrictedToPlanIds
          ? {
              set: restrictedToPlanIds.map((planId: string) => ({ id: planId })),
            }
          : undefined,
      },
      include: {
        restrictedToPlans: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    logger.log("Promotional code updated", { codeId: promotionalCode.id, code: promotionalCode.code })

    const data: z.infer<typeof promotionalCodeResponseSchema> = {
      id: promotionalCode.id,
      code: promotionalCode.code,
      name: promotionalCode.name,
      description: promotionalCode.description,
      effectType: promotionalCode.effectType,
      discountType: promotionalCode.discountType,
      discountValue: promotionalCode.discountValue,
      paymentCount: promotionalCode.paymentCount,
      isActive: promotionalCode.isActive,
      validFrom: promotionalCode.validFrom,
      validUntil: promotionalCode.validUntil,
      maxUsageCount: promotionalCode.maxUsageCount,
      currentUsageCount: promotionalCode.currentUsageCount,
      restrictedToPlans: promotionalCode.restrictedToPlans,
      createdAt: promotionalCode.createdAt,
      updatedAt: promotionalCode.updatedAt,
    }

    return data
  } catch (error: unknown) {
    return handleApiError(error)
  }
}

export const deletePromotionalCode = async ({ input, ctx: { session } }: { input: any; ctx: { session: any } }) => {
  try {
    ensureLoggedIn(session)

    // Check if user has admin role
    if (!session.user.roles.includes("ADMIN")) {
      return ApiError("unauthorized", "FORBIDDEN")
    }

    const { id } = input

    // Check if promotional code exists
    const existingCode = await prisma.promotionalCode.findUnique({
      where: { id },
      include: {
        usages: true,
        subscriptions: true,
      },
    })

    if (!existingCode) {
      return ApiError("codeNotFound")
    }

    // Check if code has been used
    if (existingCode.usages.length > 0 || existingCode.subscriptions.length > 0) {
      return ApiError("codeHasUsages")
    }

    // Delete promotional code
    await prisma.promotionalCode.delete({
      where: { id },
    })

    logger.log("Promotional code deleted", { codeId: id, code: existingCode.code })

    return { success: true }
  } catch (error: unknown) {
    return handleApiError(error)
  }
}
