import { z } from "zod"

import { prisma } from "@/lib/prisma"
import { ApiError, ensureLoggedIn, handleApiError } from "@/lib/utils/server-utils"
import { apiInputFromSchema } from "@/types"
import { logger } from "@coheadcoaching/lib"
import { BillingPeriod } from "@prisma/client"

import {
  getPromotionalCodeSchema,
  listPromotionalCodesSchema,
  promotionalCodeListResponseSchema,
  promotionalCodeResponseSchema,
  promotionalCodeValidationResponseSchema,
  validatePromotionalCodeSchema,
} from "./schemas"

export const getPromotionalCode = async ({
  input,
  ctx: { session },
}: apiInputFromSchema<typeof getPromotionalCodeSchema>) => {
  try {
    ensureLoggedIn(session)

    // Check if user has admin role
    if (!session.user.roles.includes("ADMIN")) {
      return ApiError("unauthorized", "FORBIDDEN")
    }

    const { id } = input

    const promotionalCode = await prisma.promotionalCode.findUnique({
      where: { id },
      include: {
        restrictedToPlans: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    if (!promotionalCode) {
      return ApiError("codeNotFound")
    }

    const data: z.infer<typeof promotionalCodeResponseSchema> = {
      id: promotionalCode.id,
      code: promotionalCode.code,
      name: promotionalCode.name,
      description: promotionalCode.description,
      effectType: promotionalCode.effectType,
      discountType: promotionalCode.discountType,
      discountValue: promotionalCode.discountValue,
      paymentCount: promotionalCode.paymentCount,
      isActive: promotionalCode.isActive,
      validFrom: promotionalCode.validFrom,
      validUntil: promotionalCode.validUntil,
      maxUsageCount: promotionalCode.maxUsageCount,
      currentUsageCount: promotionalCode.currentUsageCount,
      restrictedToPlans: promotionalCode.restrictedToPlans,
      createdAt: promotionalCode.createdAt,
      updatedAt: promotionalCode.updatedAt,
    }

    return data
  } catch (error: unknown) {
    return handleApiError(error)
  }
}

export const listPromotionalCodes = async ({
  input,
  ctx: { session },
}: apiInputFromSchema<typeof listPromotionalCodesSchema>) => {
  try {
    ensureLoggedIn(session)

    // Check if user has admin role
    if (!session.user.roles.includes("ADMIN")) {
      return ApiError("unauthorized", "FORBIDDEN")
    }

    const { page, limit, search, isActive, effectType } = input
    const skip = (page - 1) * limit

    const where: any = {}

    if (search) {
      where.OR = [
        { code: { contains: search, mode: "insensitive" } },
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ]
    }

    if (isActive !== undefined) {
      where.isActive = isActive
    }

    if (effectType) {
      where.effectType = effectType
    }

    const [codes, total] = await Promise.all([
      prisma.promotionalCode.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
        include: {
          restrictedToPlans: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      }),
      prisma.promotionalCode.count({ where }),
    ])

    const totalPages = Math.ceil(total / limit)

    const data: z.infer<typeof promotionalCodeListResponseSchema> = {
      codes: codes.map((code) => ({
        id: code.id,
        code: code.code,
        name: code.name,
        description: code.description,
        effectType: code.effectType,
        discountType: code.discountType,
        discountValue: code.discountValue,
        paymentCount: code.paymentCount,
        isActive: code.isActive,
        validFrom: code.validFrom,
        validUntil: code.validUntil,
        maxUsageCount: code.maxUsageCount,
        currentUsageCount: code.currentUsageCount,
        restrictedToPlans: code.restrictedToPlans,
        createdAt: code.createdAt,
        updatedAt: code.updatedAt,
      })),
      total,
      page,
      limit,
      totalPages,
    }

    return data
  } catch (error: unknown) {
    return handleApiError(error)
  }
}

export const validatePromotionalCode = async ({
  input,
  ctx: { session },
}: apiInputFromSchema<typeof validatePromotionalCodeSchema>) => {
  try {
    const { code, planId, billingPeriod } = input

    // Find the promotional code
    const promotionalCode = await prisma.promotionalCode.findUnique({
      where: { code: code.toUpperCase() },
      include: {
        restrictedToPlans: true,
      },
    })

    if (!promotionalCode) {
      const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
        isValid: false,
        error: "Promotional code not found",
      }
      return data
    }

    // Check if code is active
    if (!promotionalCode.isActive) {
      const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
        isValid: false,
        error: "Promotional code is not active",
      }
      return data
    }

    // Check validity dates
    const now = new Date()
    if (promotionalCode.validFrom && now < promotionalCode.validFrom) {
      const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
        isValid: false,
        error: "Promotional code is not yet valid",
      }
      return data
    }

    if (promotionalCode.validUntil && now > promotionalCode.validUntil) {
      const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
        isValid: false,
        error: "Promotional code has expired",
      }
      return data
    }

    // Check usage limit
    if (promotionalCode.maxUsageCount && promotionalCode.currentUsageCount >= promotionalCode.maxUsageCount) {
      const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
        isValid: false,
        error: "Promotional code usage limit reached",
      }
      return data
    }

    // Check effect type compatibility with billing period
    if (
      (promotionalCode.effectType === "MONTHLY_ONLY" && billingPeriod !== "MONTHLY") ||
      (promotionalCode.effectType === "ANNUAL_ONLY" && billingPeriod !== "ANNUAL")
    ) {
      const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
        isValid: false,
        error: `Promotional code is only valid for ${promotionalCode.effectType.toLowerCase().replace("_only", "")} billing`,
      }
      return data
    }

    // Check plan restrictions
    if (promotionalCode.restrictedToPlans.length > 0) {
      const isValidForPlan = promotionalCode.restrictedToPlans.some((plan) => plan.id === planId)
      if (!isValidForPlan) {
        const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
          isValid: false,
          error: "Promotional code is not valid for this plan",
        }
        return data
      }
    }

    // Check if user has already used this code (if session exists)
    if (session?.user?.id) {
      const existingUsage = await prisma.promotionalCodeUsage.findUnique({
        where: {
          userId_promotionalCodeId: {
            userId: session.user.id,
            promotionalCodeId: promotionalCode.id,
          },
        },
      })

      if (existingUsage) {
        const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
          isValid: false,
          error: "You have already used this promotional code",
        }
        return data
      }
    }

    // Code is valid
    const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
      isValid: true,
      code: promotionalCode.code,
      discountType: promotionalCode.discountType,
      discountValue: promotionalCode.discountValue,
      paymentCount: promotionalCode.paymentCount,
    }

    return data
  } catch (error: unknown) {
    logger.error("Error validating promotional code", error)
    const data: z.infer<typeof promotionalCodeValidationResponseSchema> = {
      isValid: false,
      error: "Error validating promotional code",
    }
    return data
  }
}
