import { z } from "zod"

import {
  PROMOTIONAL_CODE_MAX_DISCOUNT_PERCENTAGE,
  PROMOTIONAL_CODE_MAX_LENGTH,
  PROMOTIONAL_CODE_MAX_PAYMENT_COUNT,
  PROMOTIONAL_CODE_MAX_USAGE_DEFAULT,
  PROMOTIONAL_CODE_MIN_LENGTH,
} from "@/constants"

// Enums
export const promotionalCodeEffectTypeSchema = z.enum(["MONTHLY_ONLY", "ANNUAL_ONLY", "BOTH"])
export const promotionalCodeDiscountTypeSchema = z.enum(["PERCENTAGE", "FIXED_AMOUNT"])

// Base schemas
export const createPromotionalCodeSchema = z.object({
  code: z
    .string()
    .min(PROMOTIONAL_CODE_MIN_LENGTH, `Code must be at least ${PROMOTIONAL_CODE_MIN_LENGTH} characters`)
    .max(PROMOTIONAL_CODE_MAX_LENGTH, `Code must be at most ${PROMOTIONAL_CODE_MAX_LENGTH} characters`)
    .regex(/^[A-Z0-9_-]+$/, "Code can only contain uppercase letters, numbers, underscores, and hyphens"),
  name: z.string().min(1, "Name is required").max(100, "Name must be at most 100 characters"),
  description: z.string().optional(),
  effectType: promotionalCodeEffectTypeSchema,
  discountType: promotionalCodeDiscountTypeSchema,
  discountValue: z.number().int().positive("Discount value must be positive"),
  paymentCount: z
    .number()
    .int()
    .min(1, "Payment count must be at least 1")
    .max(PROMOTIONAL_CODE_MAX_PAYMENT_COUNT, `Payment count must be at most ${PROMOTIONAL_CODE_MAX_PAYMENT_COUNT}`),
  validFrom: z.date().optional(),
  validUntil: z.date().optional(),
  maxUsageCount: z.number().int().positive().optional(),
  restrictedToPlanIds: z.array(z.number().int()).optional(),
})

export const updatePromotionalCodeSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Name is required").max(100, "Name must be at most 100 characters").optional(),
  description: z.string().optional(),
  effectType: promotionalCodeEffectTypeSchema.optional(),
  discountType: promotionalCodeDiscountTypeSchema.optional(),
  discountValue: z.number().int().positive("Discount value must be positive").optional(),
  paymentCount: z
    .number()
    .int()
    .min(1, "Payment count must be at least 1")
    .max(PROMOTIONAL_CODE_MAX_PAYMENT_COUNT, `Payment count must be at most ${PROMOTIONAL_CODE_MAX_PAYMENT_COUNT}`)
    .optional(),
  isActive: z.boolean().optional(),
  validFrom: z.date().optional(),
  validUntil: z.date().optional(),
  maxUsageCount: z.number().int().positive().optional(),
  restrictedToPlanIds: z.array(z.number().int()).optional(),
})

export const validatePromotionalCodeSchema = z.object({
  code: z.string(),
  planId: z.number().int(),
  billingPeriod: z.enum(["MONTHLY", "ANNUAL"]),
})

export const applyPromotionalCodeSchema = z.object({
  code: z.string(),
  subscriptionId: z.string(),
})

export const getPromotionalCodeSchema = z.object({
  id: z.string(),
})

export const listPromotionalCodesSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  search: z.string().optional(),
  isActive: z.boolean().optional(),
  effectType: promotionalCodeEffectTypeSchema.optional(),
})

export const deletePromotionalCodeSchema = z.object({
  id: z.string(),
})

// Response schemas
export const promotionalCodeResponseSchema = z.object({
  id: z.string(),
  code: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  effectType: promotionalCodeEffectTypeSchema,
  discountType: promotionalCodeDiscountTypeSchema,
  discountValue: z.number(),
  paymentCount: z.number(),
  isActive: z.boolean(),
  validFrom: z.date().nullable(),
  validUntil: z.date().nullable(),
  maxUsageCount: z.number().nullable(),
  currentUsageCount: z.number(),
  restrictedToPlans: z.array(
    z.object({
      id: z.number(),
      name: z.string(),
    })
  ),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export const promotionalCodeValidationResponseSchema = z.object({
  isValid: z.boolean(),
  code: z.string().optional(),
  discountType: promotionalCodeDiscountTypeSchema.optional(),
  discountValue: z.number().optional(),
  paymentCount: z.number().optional(),
  error: z.string().optional(),
})

export const deletePromotionalCodeResponseSchema = z.object({
  success: z.boolean(),
})

export const promotionalCodeListResponseSchema = z.object({
  codes: z.array(promotionalCodeResponseSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
})

// Custom validation
export const createPromotionalCodeSchemaWithValidation = createPromotionalCodeSchema.refine(
  (data) => {
    if (data.discountType === "PERCENTAGE" && data.discountValue > PROMOTIONAL_CODE_MAX_DISCOUNT_PERCENTAGE) {
      return false
    }
    return true
  },
  {
    message: `Percentage discount cannot exceed ${PROMOTIONAL_CODE_MAX_DISCOUNT_PERCENTAGE}%`,
    path: ["discountValue"],
  }
)

export const updatePromotionalCodeSchemaWithValidation = updatePromotionalCodeSchema.refine(
  (data) => {
    if (
      data.discountType === "PERCENTAGE" &&
      data.discountValue &&
      data.discountValue > PROMOTIONAL_CODE_MAX_DISCOUNT_PERCENTAGE
    ) {
      return false
    }
    return true
  },
  {
    message: `Percentage discount cannot exceed ${PROMOTIONAL_CODE_MAX_DISCOUNT_PERCENTAGE}%`,
    path: ["discountValue"],
  }
)
