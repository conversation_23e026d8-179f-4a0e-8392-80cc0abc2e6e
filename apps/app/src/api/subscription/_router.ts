import { randomBytes } from "crypto"
import { addDays, addMonths, addYears } from "date-fns"
import { z } from "zod"

import { sendRenewalLinkEmail } from "@/lib/email/renewal-link"
import { env } from "@/lib/env"
import { initiateRecurringPayment } from "@/lib/mangopay"
import { prisma } from "@/lib/prisma"
import { authenticatedProcedure, modoAuthenticatedProcedure, publicProcedure, router } from "@/lib/server/trpc"
import { cancelRegistration, checkUserMangopaySetup, createSubscription } from "@/lib/subscription"
import { logger } from "@coheadcoaching/lib"
import { BillingPeriod, PaymentStatus, RenewalLinkStatus, SubscriptionStatus } from "@prisma/client"
import { TRPCError } from "@trpc/server"

export const subscriptionRouter = router({
  create: authenticatedProcedure
    .input(
      z.object({
        planId: z.number(),
        billingPeriod: z.enum([BillingPeriod.MONTHLY, BillingPeriod.ANNUAL]),
        cardId: z.string(),
        browserInfos: z.object({
          AcceptHeader: z.string(),
          JavaEnabled: z.boolean(),
          JavascriptEnabled: z.boolean(),
          Language: z.string(),
          ColorDepth: z.number(),
          ScreenHeight: z.number(),
          ScreenWidth: z.number(),
          TimeZoneOffset: z.number(),
          UserAgent: z.string(),
        }),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { planId, billingPeriod, cardId, browserInfos } = input

      if (!ctx?.session?.user.id) {
        throw new Error("User not authenticated")
      }

      const IP = ctx.req?.headers.get("x-forwarded-for") ?? ""

      // Call the updated createSubscription function
      const result = await createSubscription({
        userId: ctx.session.user.id,
        planId,
        billingPeriod,
        browserInfos,
        IP,
        mangopayCardId: cardId,
      })

      // Return the necessary data, including the redirectUrl
      return {
        subscription: result.subscription,
        payment: result.payment,
        redirectUrl: result.redirectUrl,
        returnUrl: result.returnUrl,
      }
    }),

  checkSetup: authenticatedProcedure.query(async ({ ctx }) => {
    if (!ctx?.session?.user.id) {
      throw new Error("User not authenticated")
    }
    return await checkUserMangopaySetup(ctx.session.user.id)
  }),

  getAllForAdmin: modoAuthenticatedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        pageSize: z.number().min(1).max(50).default(15),
      })
    )
    .query(async ({ input }) => {
      const { page, pageSize } = input
      const skip = (page - 1) * pageSize

      const subscriptions = await prisma.subscription.findMany({
        skip,
        take: pageSize,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          plan: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      })

      const totalCount = await prisma.subscription.count()

      return {
        data: subscriptions,
        pagination: {
          page,
          pageSize,
          totalCount,
          totalPages: Math.ceil(totalCount / pageSize),
        },
      }
    }),

  cancelByAdmin: modoAuthenticatedProcedure.input(z.string()).mutation(async ({ input }) => {
    const subscriptionId = input

    // Vérifier si l'abonnement existe
    const subscription = await prisma.subscription.findUnique({
      where: { id: subscriptionId },
    })

    if (!subscription) {
      throw new Error("Abonnement non trouvé")
    }

    // Mettre à jour le statut de l'abonnement
    const updatedSubscription = await prisma.subscription.update({
      where: { id: subscriptionId },
      data: {
        status: "CANCELED",
        canceledAt: new Date(),
      },
    })

    return updatedSubscription
  }),

  updateByAdmin: modoAuthenticatedProcedure
    .input(
      z.object({
        id: z.string(),
        planId: z.number().optional(),
        status: z
          .enum([
            SubscriptionStatus.ACTIVE,
            SubscriptionStatus.CANCELED,
            SubscriptionStatus.EXPIRED,
            SubscriptionStatus.FAILED,
            SubscriptionStatus.PENDING,
            SubscriptionStatus.AUTHENTICATION_NEEDED,
          ])
          .optional(),
        billingPeriod: z.enum([BillingPeriod.MONTHLY, BillingPeriod.ANNUAL]).optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        canceledAt: z.date().nullable().optional(),
        isAdminManaged: z.boolean().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const { id, ...updateData } = input

      // Vérifier si l'abonnement existe
      const subscription = await prisma.subscription.findUnique({
        where: { id },
        include: { plan: true },
      })

      if (!subscription) {
        throw new Error("Abonnement non trouvé")
      }

      // Si un nouveau planId est fourni, vérifier qu'il existe
      if (updateData.planId) {
        const plan = await prisma.plan.findUnique({
          where: { id: updateData.planId },
        })

        if (!plan) {
          throw new Error("Plan non trouvé")
        }

        // Pour les abonnements admin-managed, permettre de lier à des plans inactifs
        if (!plan.isActive && !updateData.isAdminManaged && !subscription.isAdminManaged) {
          throw new Error("Impossible de lier un abonnement standard à un plan inactif")
        }
      }

      // Si le statut passe à CANCELED, définir canceledAt à la date actuelle
      // sauf si canceledAt est explicitement fourni
      if (updateData.status === "CANCELED" && updateData.canceledAt === undefined) {
        updateData.canceledAt = new Date()
      }

      // Si le statut passe à ACTIVE depuis CANCELED, réinitialiser canceledAt
      if (updateData.status === "ACTIVE" && subscription.status === "CANCELED") {
        updateData.canceledAt = null
      }

      // Mettre à jour l'abonnement
      const updatedSubscription = await prisma.subscription.update({
        where: { id },
        data: updateData,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          plan: true,
        },
      })

      return updatedSubscription
    }),

  // Create admin-managed subscription
  createAdminManaged: modoAuthenticatedProcedure
    .input(
      z.object({
        userId: z.string(),
        planId: z.number(),
        billingPeriod: z.enum([BillingPeriod.MONTHLY, BillingPeriod.ANNUAL]),
        startDate: z.date(),
        endDate: z.date(),
      })
    )
    .mutation(async ({ input }) => {
      const { userId, planId, billingPeriod, startDate, endDate } = input

      // Verify user exists
      const user = await prisma.user.findUnique({
        where: { id: userId },
      })

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Utilisateur non trouvé",
        })
      }

      // Verify plan exists (can be inactive for admin-managed subscriptions)
      const plan = await prisma.plan.findUnique({
        where: { id: planId },
      })

      if (!plan) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Plan non trouvé",
        })
      }

      // Create admin-managed subscription
      const subscription = await prisma.subscription.create({
        data: {
          userId,
          planId,
          billingPeriod,
          startDate,
          endDate,
          status: SubscriptionStatus.ACTIVE,
          isAdminManaged: true,
          // Admin-managed subscriptions don't need MangoPay registration
          mangopayRecurringRegistrationId: null,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          plan: true,
        },
      })

      logger.log(`Admin created managed subscription ${subscription.id} for user ${userId}`)

      return subscription
    }),

  getUserActivePlan: authenticatedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session?.user.id

    if (!userId) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "Utilisateur non authentifié" })
    }

    // Récupérer l'abonnement actif de l'utilisateur
    const activeSubscription = await prisma.subscription.findFirst({
      where: {
        userId,
        status: "ACTIVE",
      },
      include: {
        plan: true,
      },
    })

    if (!activeSubscription) {
      return null
    }

    // Récupérer les restrictions du plan
    const planRestrictions = await prisma.planRestriction.findMany({
      where: {
        planId: activeSubscription.planId,
      },
    })

    // Convertir les features de JSON string à objets
    const features = activeSubscription.plan.features.map((feature) => {
      try {
        return JSON.parse(feature)
      } catch (e) {
        return { text: feature, included: true }
      }
    })

    return {
      ...activeSubscription.plan,
      features,
      restrictions: planRestrictions,
    }
  }),

  // Get current user's subscription details
  getCurrentSubscription: authenticatedProcedure.query(async ({ ctx }) => {
    const userId = ctx.session?.user.id

    if (!userId) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "Utilisateur non authentifié" })
    }

    const subscription = await prisma.subscription.findFirst({
      where: {
        userId,
        status: { in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.PENDING] },
      },
      include: {
        plan: {
          include: {
            restrictions: true,
          },
        },
        payments: {
          orderBy: { createdAt: "desc" },
          take: 5,
        },
      },
      orderBy: { createdAt: "desc" },
    })

    if (!subscription) {
      return null
    }

    // Parse plan features
    const features = subscription.plan.features.map((feature) => {
      try {
        return JSON.parse(feature)
      } catch (e) {
        return { text: feature, included: true }
      }
    })

    return {
      ...subscription,
      plan: {
        ...subscription.plan,
        features,
      },
    }
  }),

  // Check if there's an active cancellation form
  getCancellationForm: authenticatedProcedure.query(async () => {
    const form = await prisma.form.findFirst({
      where: {
        type: "SUBSCRIPTION_CANCELLATION",
        status: "ACTIVE",
        isActive: true,
      },
      include: {
        questions: {
          orderBy: { order: "asc" },
          select: {
            id: true,
            title: true,
            description: true,
            type: true,
            order: true,
            isRequired: true,
            minLength: true,
            maxLength: true,
            minValue: true,
            maxValue: true,
            options: true,
            showConditions: true,
          },
        },
      },
    })

    return form
  }),

  // Cancel user's subscription (with optional form submission)
  cancelMySubscription: authenticatedProcedure
    .input(
      z.object({
        subscriptionId: z.string().optional(),
        formSubmissionId: z.string().optional(), // If form was submitted
      })
    )
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session?.user.id

      if (!userId) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "Utilisateur non authentifié" })
      }

      try {
        // Check if there's an active cancellation form
        const activeCancellationForm = await prisma.form.findFirst({
          where: {
            type: "SUBSCRIPTION_CANCELLATION",
            status: "ACTIVE",
            isActive: true,
          },
        })

        // If there's an active form but no submission provided, require form completion
        if (activeCancellationForm && !input.formSubmissionId) {
          throw new TRPCError({
            code: "PRECONDITION_FAILED",
            message: "Veuillez d'abord remplir le formulaire d'annulation",
            cause: { requiresForm: true, formId: activeCancellationForm.id },
          })
        }

        // If form submission is provided, verify it exists and belongs to the user
        if (input.formSubmissionId) {
          const submission = await prisma.formSubmission.findFirst({
            where: {
              id: input.formSubmissionId,
              userId,
              isComplete: true,
            },
          })

          if (!submission) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Soumission de formulaire non trouvée ou incomplète",
            })
          }
        }

        const result = await cancelRegistration(userId, input.subscriptionId)
        return result
      } catch (error) {
        // Re-throw TRPCError as-is
        if (error instanceof TRPCError) {
          throw error
        }

        logger.error("Error canceling subscription", { error })

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error instanceof Error ? error.message : "Erreur lors de l'annulation de l'abonnement",
        })
      }
    }),

  // Generate renewal link for auth-needed subscriptions
  generateRenewalLink: modoAuthenticatedProcedure
    .input(
      z.object({
        subscriptionId: z.string(),
      })
    )
    .mutation(async ({ input }) => {
      const { subscriptionId } = input

      try {
        // Verify subscription exists and can have renewal links
        const subscription = await prisma.subscription.findUnique({
          where: { id: subscriptionId },
          include: {
            user: {
              select: { id: true, email: true, name: true },
            },
            plan: true,
          },
        })

        if (!subscription) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Abonnement non trouvé",
          })
        }

        // Generate a secure random token
        const token = randomBytes(32).toString("hex")

        // Set expiration to 30 days from now
        const expiresAt = addDays(new Date(), 30)

        // Check if there's already a pending renewal link for this subscription
        const existingLink = await prisma.renewalLink.findFirst({
          where: {
            subscriptionId,
            status: RenewalLinkStatus.PENDING,
            expiresAt: {
              gt: new Date(),
            },
          },
        })

        if (existingLink) {
          return {
            renewalLink: existingLink,
            renewalUrl: `/subscription/renew/${existingLink.token}`,
          }
        }

        // Create new renewal link
        const renewalLink = await prisma.renewalLink.create({
          data: {
            token,
            subscriptionId,
            status: RenewalLinkStatus.PENDING,
            expiresAt,
          },
        })

        logger.log(`Admin generated renewal link ${renewalLink.id} for subscription ${subscriptionId}`)

        // Send email notification to user
        try {
          await sendRenewalLinkEmail({
            userEmail: subscription.user.email!,
            userName: subscription.user.name || "Utilisateur",
            token,
            subscription: {
              id: subscription.id,
              plan: {
                name: subscription.plan.name,
              },
              billingPeriod: subscription.billingPeriod,
            },
            expiresAt: renewalLink.expiresAt,
          })
          logger.log(`Renewal link email sent for subscription ${subscriptionId}`)
        } catch (emailError) {
          logger.error(`Failed to send renewal link email for subscription ${subscriptionId}`, { emailError })
          // Don't fail the entire operation if email fails
        }

        return {
          renewalLink,
          renewalUrl: `/subscription/renew/${token}`,
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }

        logger.error("Error generating renewal link", { error })

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error instanceof Error ? error.message : "Erreur lors de la génération du lien de renouvellement",
        })
      }
    }),

  // Validate renewal token (public endpoint for the renewal page)
  validateRenewalToken: publicProcedure
    .input(
      z.object({
        token: z.string(),
      })
    )
    .query(async ({ input }) => {
      const { token } = input

      try {
        const renewalLink = await prisma.renewalLink.findUnique({
          where: { token },
          include: {
            subscription: {
              include: {
                user: {
                  select: { id: true, email: true, name: true },
                },
                plan: true,
              },
            },
          },
        })

        if (!renewalLink) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Lien de renouvellement non trouvé",
          })
        }

        if (renewalLink.status !== RenewalLinkStatus.PENDING) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Ce lien de renouvellement a déjà été utilisé ou a expiré",
          })
        }

        if (renewalLink.expiresAt < new Date()) {
          // Mark as expired
          await prisma.renewalLink.update({
            where: { id: renewalLink.id },
            data: { status: RenewalLinkStatus.EXPIRED },
          })

          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Ce lien de renouvellement a expiré",
          })
        }

        return {
          renewalLink,
          subscription: renewalLink.subscription,
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }

        logger.error("Error validating renewal token", { error })

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erreur lors de la validation du lien de renouvellement",
        })
      }
    }),

  // Process customer-initiated renewal
  processRenewal: publicProcedure
    .input(
      z.object({
        token: z.string(),
        browserInfos: z.object({
          AcceptHeader: z.string(),
          JavaEnabled: z.boolean(),
          JavascriptEnabled: z.boolean(),
          Language: z.string(),
          ColorDepth: z.number(),
          ScreenHeight: z.number(),
          ScreenWidth: z.number(),
          TimeZoneOffset: z.number(),
          UserAgent: z.string(),
        }),
        ipAddress: z.string().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const { token, browserInfos, ipAddress } = input

      try {
        // First validate the token
        const renewalLink = await prisma.renewalLink.findUnique({
          where: { token },
          include: {
            subscription: {
              include: {
                user: true,
                plan: true,
              },
            },
          },
        })

        if (!renewalLink) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Lien de renouvellement non trouvé",
          })
        }

        if (renewalLink.status !== RenewalLinkStatus.PENDING) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Ce lien de renouvellement a déjà été utilisé ou a expiré",
          })
        }

        if (renewalLink.expiresAt < new Date()) {
          await prisma.renewalLink.update({
            where: { id: renewalLink.id },
            data: { status: RenewalLinkStatus.EXPIRED },
          })

          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Ce lien de renouvellement a expiré",
          })
        }

        // Mark the renewal link as completed and store browser info for compliance
        await prisma.renewalLink.update({
          where: { id: renewalLink.id },
          data: {
            status: RenewalLinkStatus.COMPLETED,
            completedAt: new Date(),
            browserInfo: browserInfos,
            ipAddress: ipAddress || ctx.req?.headers.get("x-forwarded-for") || "",
          },
        })

        // Process the actual renewal payment using customer-initiated transaction (CIT)
        if (!renewalLink.subscription.mangopayRecurringRegistrationId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Abonnement sans enregistrement de paiement récurrent",
          })
        }

        // Calculate payment amount
        const plan = renewalLink.subscription.plan
        const amountInEuros =
          renewalLink.subscription.billingPeriod === BillingPeriod.MONTHLY
            ? plan.monthlyPrice / 100
            : plan.annualPrice / 100
        const amountInCents = Math.round(amountInEuros * 100)

        // Create payment record
        const paymentRecord = await prisma.payment.create({
          data: {
            subscriptionId: renewalLink.subscriptionId,
            amount: amountInEuros,
            currency: "EUR",
            status: PaymentStatus.PENDING,
          },
        })

        try {
          // Process customer-initiated transaction
          const secureModeReturnURL = `${env.NEXT_PUBLIC_BASE_URL}/subscription/return?subscriptionId=${renewalLink.subscriptionId}&paymentId=${paymentRecord.id}&renewal=true`

          const citResult = await initiateRecurringPayment({
            recurringPayinRegistrationId: renewalLink.subscription.mangopayRecurringRegistrationId,
            tag: `CoheadCoaching CIT Renewal - Sub: ${renewalLink.subscriptionId}, Payment: ${paymentRecord.id}`,
            amount: amountInCents,
            currency: "EUR",
            secureModeReturnURL,
            statementDescriptor: `ABO ${plan.name.substring(0, 6)}`,
            browserInfo: browserInfos,
            ipAddress: ipAddress || ctx.req?.headers.get("x-forwarded-for") || "",
          })

          // Update payment record with MangoPay result
          await prisma.payment.update({
            where: { id: paymentRecord.id },
            data: {
              mangopayPayinId: citResult.Id,
              status: citResult.Status === "SUCCEEDED" ? PaymentStatus.SUCCEEDED : PaymentStatus.PENDING,
              failureReason: citResult.ResultMessage || null,
            },
          })

          logger.log("CIT", citResult)

          if (citResult.Status === "SUCCEEDED") {
            // Payment succeeded, update subscription
            const newEndDate =
              renewalLink.subscription.billingPeriod === BillingPeriod.MONTHLY
                ? addMonths(renewalLink.subscription.endDate, 1)
                : addYears(renewalLink.subscription.endDate, 1)

            await prisma.subscription.update({
              where: { id: renewalLink.subscriptionId },
              data: {
                status: SubscriptionStatus.ACTIVE,
                endDate: newEndDate,
              },
            })

            logger.log(
              `CIT renewal payment succeeded for subscription ${renewalLink.subscriptionId}. New end date: ${newEndDate.toISOString()}`
            )
          } else if (citResult.SecureModeRedirectURL) {
            // 3DS authentication required
            logger.log(`CIT renewal requires 3DS authentication for subscription ${renewalLink.subscriptionId}`)

            return {
              success: true,
              requiresRedirect: true,
              redirectUrl: citResult.SecureModeRedirectURL,
              message: "Authentification 3DS requise",
              subscriptionId: renewalLink.subscriptionId,
              paymentId: paymentRecord.id,
            }
          } else {
            // Payment failed
            await prisma.payment.update({
              where: { id: paymentRecord.id },
              data: {
                status: PaymentStatus.FAILED,
                failureReason: citResult.ResultMessage || "Paiement échoué",
              },
            })

            // Mark renewal link as failed (we could add a FAILED status)
            await prisma.renewalLink.update({
              where: { id: renewalLink.id },
              data: {
                status: RenewalLinkStatus.EXPIRED, // Using EXPIRED as closest status
              },
            })

            throw new TRPCError({
              code: "BAD_REQUEST",
              message: `Échec du paiement de renouvellement: ${citResult.ResultMessage || "Erreur inconnue"}`,
            })
          }
        } catch (paymentError) {
          // Update payment record as failed
          await prisma.payment.update({
            where: { id: paymentRecord.id },
            data: {
              status: PaymentStatus.FAILED,
              failureReason: paymentError instanceof Error ? paymentError.message : "Erreur système",
            },
          })

          logger.error(`CIT renewal payment failed for subscription ${renewalLink.subscriptionId}`, { paymentError })

          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Erreur lors du traitement du paiement de renouvellement",
          })
        }

        logger.log(
          `Processed customer-initiated renewal for subscription ${renewalLink.subscriptionId} via token ${token}`
        )

        return {
          success: true,
          message: "Renouvellement traité avec succès",
          subscriptionId: renewalLink.subscriptionId,
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }

        logger.error("Error processing renewal", { error })

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error instanceof Error ? error.message : "Erreur lors du traitement du renouvellement",
        })
      }
    }),
})
