"use client"

import React, { useState } from "react"
import { useRouter } from "next/navigation"
import { AlertCircle, Tag } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { getBrowserInfo } from "@/lib/utils/browser-info"
import { But<PERSON> } from "@nextui-org/button"
import { Card, CardBody } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Input } from "@nextui-org/input"

export default function CheckoutForm({
  planId,
  period,
  cardId,
  mangopayCardId,
  price,
  periodText,
}: {
  planId: number
  period: "MONTHLY" | "ANNUAL"
  cardId: string
  mangopayCardId: string
  price: number
  periodText: string
}) {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null)
  const [promotionalCode, setPromotionalCode] = useState("")
  const [isValidatingCode, setIsValidatingCode] = useState(false)
  const [codeValidation, setCodeValidation] = useState<{
    isValid: boolean
    discountType?: string
    discountValue?: number
    error?: string
  } | null>(null)

  const utils = trpc.useUtils()

  const createSubscription = trpc.subscription.create.useMutation({
    onSuccess: (data) => {
      setError(null)

      // --- CAS 1: Redirection 3DS nécessaire ---
      if (data.redirectUrl) {
        toast.info("Finalisation sécurisée en cours...", { autoClose: false, isLoading: true })
        setTimeout(() => {
          window.location.href = data.redirectUrl!
        }, 500) // Délai court
        return
      }

      toast.info("Traitement en cours...", { autoClose: false, isLoading: true })
      router.push(data.returnUrl || "/subscription/pending")
    },
    onError: (error) => {
      const errorMsg = error.message || "Une erreur de communication est survenue. Veuillez réessayer."
      setError(errorMsg)
    },
  })

  const handleConfirmSubscription = () => {
    setError(null)

    if (!mangopayCardId) {
      const msg = "Erreur: Impossible d'identifier la carte de paiement."
      setError(msg)
      console.error("Erreur critique: mangopayCardId manquant dans CheckoutForm")
      return
    }

    createSubscription.mutate({
      planId,
      billingPeriod: period,
      cardId: mangopayCardId,
      promotionalCode: codeValidation?.isValid ? promotionalCode : undefined,
      browserInfos: getBrowserInfo(),
    })
  }

  const handleValidateCode = async () => {
    if (!promotionalCode.trim()) {
      setCodeValidation(null)
      return
    }

    setIsValidatingCode(true)
    try {
      const result = await utils.promotionalCode.validate.fetch({
        code: promotionalCode.trim(),
        planId,
        billingPeriod: period,
      })
      setCodeValidation(result)
    } catch (error) {
      setCodeValidation({
        isValid: false,
        error: "Erreur lors de la validation du code",
      })
    } finally {
      setIsValidatingCode(false)
    }
  }

  const handleCodeChange = (value: string) => {
    setPromotionalCode(value)
    if (!value.trim()) {
      setCodeValidation(null)
    }
  }

  const calculateDiscountedPrice = () => {
    if (!codeValidation?.isValid || !codeValidation.discountValue) {
      return price
    }

    if (codeValidation.discountType === "PERCENTAGE") {
      return price - (price * codeValidation.discountValue) / 100
    } else {
      return Math.max(0, price - codeValidation.discountValue)
    }
  }

  const discountedPrice = calculateDiscountedPrice()
  const formattedPrice = Intl.NumberFormat("fr-FR", { style: "currency", currency: "EUR" }).format(discountedPrice / 100)
  const originalFormattedPrice = Intl.NumberFormat("fr-FR", { style: "currency", currency: "EUR" }).format(price / 100)

  return (
    <div className="mt-6 space-y-6">
      {error && (
        <div className="mb-4 flex w-full items-center gap-2 rounded-lg border border-danger-200 bg-danger-50 p-3">
          <AlertCircle className="size-5 shrink-0 text-danger" />
          <p className="text-sm text-danger">{error}</p>
        </div>
      )}

      {/* Promotional Code Section */}
      <Card>
        <CardBody className="space-y-4">
          <div className="flex items-center gap-2">
            <Tag className="size-4 text-primary" />
            <h3 className="font-medium">Code promotionnel (optionnel)</h3>
          </div>

          <div className="flex gap-2">
            <Input
              placeholder="Entrez votre code promotionnel"
              value={promotionalCode}
              onChange={(e) => handleCodeChange(e.target.value)}
              onBlur={handleValidateCode}
              isDisabled={createSubscription.isPending}
              className="flex-1"
            />
            <Button
              variant="bordered"
              onPress={handleValidateCode}
              isLoading={isValidatingCode}
              isDisabled={!promotionalCode.trim() || createSubscription.isPending}
            >
              Valider
            </Button>
          </div>

          {codeValidation && (
            <div className="mt-2">
              {codeValidation.isValid ? (
                <div className="flex items-center gap-2">
                  <Chip color="success" variant="flat" size="sm">
                    ✓ Code valide
                  </Chip>
                  <span className="text-sm text-success">
                    Remise de {codeValidation.discountType === "PERCENTAGE"
                      ? `${codeValidation.discountValue}%`
                      : `${(codeValidation.discountValue! / 100).toFixed(2)}€`}
                  </span>
                </div>
              ) : (
                <Chip color="danger" variant="flat" size="sm">
                  {codeValidation.error || "Code invalide"}
                </Chip>
              )}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Price Summary */}
      {codeValidation?.isValid && discountedPrice !== price && (
        <Card>
          <CardBody>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Prix original:</span>
                <span className="text-sm line-through text-gray-500">{originalFormattedPrice}/{periodText}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Remise:</span>
                <span className="text-sm text-success">
                  -{Intl.NumberFormat("fr-FR", { style: "currency", currency: "EUR" }).format((price - discountedPrice) / 100)}
                </span>
              </div>
              <div className="flex justify-between items-center font-semibold text-lg border-t pt-2">
                <span>Total:</span>
                <span className="text-primary">{formattedPrice}/{periodText}</span>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
        <Button
          variant="bordered"
          onPress={() => router.push("/pricing")}
          isDisabled={createSubscription.isPending}
          className="w-full sm:w-auto"
        >
          Annuler
        </Button>
        <Button
          color="primary"
          size="lg"
          onPress={handleConfirmSubscription}
          isLoading={createSubscription.isPending}
          isDisabled={!cardId || !mangopayCardId || createSubscription.isPending} // Désactiver pendant le chargement
          className="w-full px-8 font-semibold sm:w-auto"
        >
          {createSubscription.isPending ? "Traitement..." : `Payer ${formattedPrice}/${periodText}`}
        </Button>
      </div>
      <p className="mt-4 text-center text-xs text-default-500 sm:text-left">
        En cliquant sur &quot;Payer&quot;, vous acceptez nos{" "}
        <a href="/terms" target="_blank" className="underline hover:text-primary">
          Conditions Générales de Vente
        </a>
        .
      </p>
    </div>
  )
}
