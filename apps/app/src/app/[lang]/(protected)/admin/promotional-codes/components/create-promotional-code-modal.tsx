"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, ModalFooter } from "@nextui-org/modal"
import { But<PERSON> } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { Textarea } from "@nextui-org/input"
import { Select, SelectItem } from "@nextui-org/select"
import { Switch } from "@nextui-org/switch"
import { DatePicker } from "@nextui-org/date-picker"
import { Chip } from "@nextui-org/chip"
import { Card, CardBody } from "@nextui-org/card"

const createPromotionalCodeSchema = z.object({
  code: z.string().min(3).max(50).regex(/^[A-Z0-9_-]+$/, "Code can only contain uppercase letters, numbers, underscores, and hyphens"),
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  effectType: z.enum(["MONTHLY_ONLY", "ANNUAL_ONLY", "BOTH"]),
  discountType: z.enum(["PERCENTAGE", "FIXED_AMOUNT"]),
  discountValue: z.number().positive(),
  paymentCount: z.number().int().min(1).max(12),
  validFrom: z.date().optional(),
  validUntil: z.date().optional(),
  maxUsageCount: z.number().int().positive().optional(),
  restrictedToPlanIds: z.array(z.number()).optional(),
})

type CreatePromotionalCodeForm = z.infer<typeof createPromotionalCodeSchema>

interface CreatePromotionalCodeModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

export default function CreatePromotionalCodeModal({
  isOpen,
  onClose,
  onSuccess,
}: CreatePromotionalCodeModalProps) {
  const [selectedPlans, setSelectedPlans] = useState<Set<string>>(new Set())

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<CreatePromotionalCodeForm>({
    resolver: zodResolver(createPromotionalCodeSchema),
    defaultValues: {
      effectType: "BOTH",
      discountType: "PERCENTAGE",
      paymentCount: 1,
    },
  })

  const discountType = watch("discountType")

  const { data: plansData } = trpc.plan.list.useQuery()

  const createCodeMutation = trpc.promotionalCode.create.useMutation({
    onSuccess: () => {
      reset()
      setSelectedPlans(new Set())
      onSuccess()
    },
  })

  const onSubmit = (data: CreatePromotionalCodeForm) => {
    const restrictedToPlanIds = selectedPlans.size > 0 
      ? Array.from(selectedPlans).map(id => parseInt(id))
      : undefined

    createCodeMutation.mutate({
      ...data,
      code: data.code.toUpperCase(),
      restrictedToPlanIds,
    })
  }

  const handleClose = () => {
    reset()
    setSelectedPlans(new Set())
    onClose()
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="2xl" scrollBehavior="inside">
      <ModalContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalHeader>
            <h2 className="text-xl font-semibold">Créer un code promotionnel</h2>
          </ModalHeader>
          <ModalBody className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Code"
                placeholder="PROMO2024"
                {...register("code")}
                isInvalid={!!errors.code}
                errorMessage={errors.code?.message}
                description="Lettres majuscules, chiffres, tirets et underscores uniquement"
              />
              <Input
                label="Nom"
                placeholder="Promotion de Noël"
                {...register("name")}
                isInvalid={!!errors.name}
                errorMessage={errors.name?.message}
              />
            </div>

            <Textarea
              label="Description (optionnel)"
              placeholder="Description du code promotionnel..."
              {...register("description")}
            />

            <div className="grid grid-cols-2 gap-4">
              <Select
                label="Type d'effet"
                {...register("effectType")}
                defaultSelectedKeys={["BOTH"]}
              >
                <SelectItem key="MONTHLY_ONLY">Mensuel uniquement</SelectItem>
                <SelectItem key="ANNUAL_ONLY">Annuel uniquement</SelectItem>
                <SelectItem key="BOTH">Les deux</SelectItem>
              </Select>

              <Select
                label="Type de remise"
                {...register("discountType")}
                defaultSelectedKeys={["PERCENTAGE"]}
                onChange={(e) => setValue("discountType", e.target.value as "PERCENTAGE" | "FIXED_AMOUNT")}
              >
                <SelectItem key="PERCENTAGE">Pourcentage</SelectItem>
                <SelectItem key="FIXED_AMOUNT">Montant fixe</SelectItem>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Input
                label={discountType === "PERCENTAGE" ? "Pourcentage de remise" : "Montant de remise (en centimes)"}
                type="number"
                placeholder={discountType === "PERCENTAGE" ? "10" : "500"}
                {...register("discountValue", { valueAsNumber: true })}
                isInvalid={!!errors.discountValue}
                errorMessage={errors.discountValue?.message}
                endContent={discountType === "PERCENTAGE" ? "%" : "centimes"}
              />
              <Input
                label="Nombre de paiements"
                type="number"
                placeholder="1"
                {...register("paymentCount", { valueAsNumber: true })}
                isInvalid={!!errors.paymentCount}
                errorMessage={errors.paymentCount?.message}
                description="Nombre de paiements auxquels la remise s'applique"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <DatePicker
                label="Date de début (optionnel)"
                onChange={(date) => setValue("validFrom", date?.toDate())}
              />
              <DatePicker
                label="Date de fin (optionnel)"
                onChange={(date) => setValue("validUntil", date?.toDate())}
              />
            </div>

            <Input
              label="Limite d'utilisation (optionnel)"
              type="number"
              placeholder="100"
              {...register("maxUsageCount", { valueAsNumber: true })}
              description="Nombre maximum d'utilisations du code"
            />

            {plansData && plansData.length > 0 && (
              <Card>
                <CardBody>
                  <h4 className="font-medium mb-2">Plans autorisés (optionnel)</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Si aucun plan n'est sélectionné, le code sera valide pour tous les plans
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {plansData.map((plan) => (
                      <Chip
                        key={plan.id}
                        variant={selectedPlans.has(plan.id.toString()) ? "solid" : "bordered"}
                        color={selectedPlans.has(plan.id.toString()) ? "primary" : "default"}
                        className="cursor-pointer"
                        onClick={() => {
                          const newSelected = new Set(selectedPlans)
                          if (newSelected.has(plan.id.toString())) {
                            newSelected.delete(plan.id.toString())
                          } else {
                            newSelected.add(plan.id.toString())
                          }
                          setSelectedPlans(newSelected)
                        }}
                      >
                        {plan.name}
                      </Chip>
                    ))}
                  </div>
                </CardBody>
              </Card>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={handleClose}>
              Annuler
            </Button>
            <Button
              color="primary"
              type="submit"
              isLoading={createCodeMutation.isPending}
            >
              Créer le code
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  )
}
