"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ooter } from "@nextui-org/modal"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { Textarea } from "@nextui-org/input"
import { Select, SelectItem } from "@nextui-org/select"
import { Switch } from "@nextui-org/switch"
import { DatePicker } from "@nextui-org/date-picker"
import { Chip } from "@nextui-org/chip"
import { Card, CardBody } from "@nextui-org/card"
import { Spinner } from "@nextui-org/spinner"

const updatePromotionalCodeSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  effectType: z.enum(["MONTHLY_ONLY", "ANNUAL_ONLY", "BOTH"]).optional(),
  discountType: z.enum(["PERCENTAGE", "FIXED_AMOUNT"]).optional(),
  discountValue: z.number().positive().optional(),
  paymentCount: z.number().int().min(1).max(12).optional(),
  isActive: z.boolean().optional(),
  validFrom: z.date().optional(),
  validUntil: z.date().optional(),
  maxUsageCount: z.number().int().positive().optional(),
  restrictedToPlanIds: z.array(z.number()).optional(),
})

type UpdatePromotionalCodeForm = z.infer<typeof updatePromotionalCodeSchema>

interface EditPromotionalCodeModalProps {
  isOpen: boolean
  onClose: () => void
  codeId: string
  onSuccess: () => void
}

export default function EditPromotionalCodeModal({
  isOpen,
  onClose,
  codeId,
  onSuccess,
}: EditPromotionalCodeModalProps) {
  const [selectedPlans, setSelectedPlans] = useState<Set<string>>(new Set())

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<UpdatePromotionalCodeForm>({
    resolver: zodResolver(updatePromotionalCodeSchema),
  })

  const discountType = watch("discountType")

  const { data: codeData, isLoading: isLoadingCode } = trpc.promotionalCode.get.useQuery(
    { id: codeId },
    { enabled: isOpen && !!codeId }
  )

  const { data: plansData } = trpc.plan.getAll.useQuery()

  const updateCodeMutation = trpc.promotionalCode.update.useMutation({
    onSuccess: () => {
      onSuccess()
    },
  })

  useEffect(() => {
    if (codeData) {
      setValue("name", codeData.name)
      setValue("description", codeData.description || "")
      setValue("effectType", codeData.effectType)
      setValue("discountType", codeData.discountType)
      setValue("discountValue", codeData.discountValue)
      setValue("paymentCount", codeData.paymentCount)
      setValue("isActive", codeData.isActive)
      setValue("validFrom", codeData.validFrom || undefined)
      setValue("validUntil", codeData.validUntil || undefined)
      setValue("maxUsageCount", codeData.maxUsageCount || undefined)

      const planIds = new Set(codeData.restrictedToPlans.map(plan => plan.id.toString()))
      setSelectedPlans(planIds)
    }
  }, [codeData, setValue])

  const onSubmit = (data: UpdatePromotionalCodeForm) => {
    const restrictedToPlanIds = selectedPlans.size > 0
      ? Array.from(selectedPlans).map(id => parseInt(id))
      : []

    updateCodeMutation.mutate({
      id: codeId,
      ...data,
      restrictedToPlanIds,
    })
  }

  const handleClose = () => {
    reset()
    setSelectedPlans(new Set())
    onClose()
  }

  if (isLoadingCode) {
    return (
      <Modal isOpen={isOpen} onClose={handleClose}>
        <ModalContent>
          <ModalBody className="flex justify-center items-center p-8">
            <Spinner size="lg" />
          </ModalBody>
        </ModalContent>
      </Modal>
    )
  }

  if (!codeData) {
    return null
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="2xl" scrollBehavior="inside">
      <ModalContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalHeader>
            <div>
              <h2 className="text-xl font-semibold">Modifier le code promotionnel</h2>
              <p className="text-sm text-gray-600">Code: {codeData.code}</p>
            </div>
          </ModalHeader>
          <ModalBody className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Nom"
                {...register("name")}
                isInvalid={!!errors.name}
                errorMessage={errors.name?.message}
              />
              <div className="flex items-center gap-2">
                <Switch
                  {...register("isActive")}
                  defaultSelected={codeData.isActive}
                  onChange={(checked) => setValue("isActive", checked)}
                >
                  Code actif
                </Switch>
              </div>
            </div>

            <Textarea
              label="Description (optionnel)"
              {...register("description")}
            />

            <div className="grid grid-cols-2 gap-4">
              <Select
                label="Type d'effet"
                {...register("effectType")}
                selectedKeys={[codeData.effectType]}
                onChange={(e) => setValue("effectType", e.target.value as any)}
              >
                <SelectItem key="MONTHLY_ONLY">Mensuel uniquement</SelectItem>
                <SelectItem key="ANNUAL_ONLY">Annuel uniquement</SelectItem>
                <SelectItem key="BOTH">Les deux</SelectItem>
              </Select>

              <Select
                label="Type de remise"
                {...register("discountType")}
                selectedKeys={[codeData.discountType]}
                onChange={(e) => setValue("discountType", e.target.value as any)}
              >
                <SelectItem key="PERCENTAGE">Pourcentage</SelectItem>
                <SelectItem key="FIXED_AMOUNT">Montant fixe</SelectItem>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Input
                label={discountType === "PERCENTAGE" ? "Pourcentage de remise" : "Montant de remise (en centimes)"}
                type="number"
                {...register("discountValue", { valueAsNumber: true })}
                isInvalid={!!errors.discountValue}
                errorMessage={errors.discountValue?.message}
                endContent={discountType === "PERCENTAGE" ? "%" : "centimes"}
              />
              <Input
                label="Nombre de paiements"
                type="number"
                {...register("paymentCount", { valueAsNumber: true })}
                isInvalid={!!errors.paymentCount}
                errorMessage={errors.paymentCount?.message}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <DatePicker
                label="Date de début (optionnel)"
                defaultValue={codeData.validFrom ? new Date(codeData.validFrom) : undefined}
                onChange={(date) => setValue("validFrom", date?.toDate())}
              />
              <DatePicker
                label="Date de fin (optionnel)"
                defaultValue={codeData.validUntil ? new Date(codeData.validUntil) : undefined}
                onChange={(date) => setValue("validUntil", date?.toDate())}
              />
            </div>

            <Input
              label="Limite d'utilisation (optionnel)"
              type="number"
              {...register("maxUsageCount", { valueAsNumber: true })}
            />

            {plansData && plansData.length > 0 && (
              <Card>
                <CardBody>
                  <h4 className="font-medium mb-2">Plans autorisés</h4>
                  <div className="flex flex-wrap gap-2">
                    {plansData.map((plan) => (
                      <Chip
                        key={plan.id}
                        variant={selectedPlans.has(plan.id.toString()) ? "solid" : "bordered"}
                        color={selectedPlans.has(plan.id.toString()) ? "primary" : "default"}
                        className="cursor-pointer"
                        onClick={() => {
                          const newSelected = new Set(selectedPlans)
                          if (newSelected.has(plan.id.toString())) {
                            newSelected.delete(plan.id.toString())
                          } else {
                            newSelected.add(plan.id.toString())
                          }
                          setSelectedPlans(newSelected)
                        }}
                      >
                        {plan.name}
                      </Chip>
                    ))}
                  </div>
                </CardBody>
              </Card>
            )}

            <Card>
              <CardBody>
                <h4 className="font-medium mb-2">Statistiques d'utilisation</h4>
                <p className="text-sm text-gray-600">
                  Utilisations actuelles: {codeData.currentUsageCount}
                  {codeData.maxUsageCount && ` / ${codeData.maxUsageCount}`}
                </p>
              </CardBody>
            </Card>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={handleClose}>
              Annuler
            </Button>
            <Button
              color="primary"
              type="submit"
              isLoading={updateCodeMutation.isPending}
            >
              Mettre à jour
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  )
}
