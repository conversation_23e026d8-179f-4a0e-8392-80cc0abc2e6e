"use client"

import { format } from "date-fns"
import { fr } from "date-fns/locale"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON>, <PERSON>dal<PERSON>ontent, ModalHeader, ModalBody, ModalFooter } from "@nextui-org/modal"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Spinner } from "@nextui-org/spinner"
import { Divider } from "@nextui-org/divider"

interface ViewPromotionalCodeModalProps {
  isOpen: boolean
  onClose: () => void
  codeId: string
}

export default function ViewPromotionalCodeModal({
  isOpen,
  onClose,
  codeId,
}: ViewPromotionalCodeModalProps) {
  const { data: codeData, isLoading } = trpc.promotionalCode.get.useQuery(
    { id: codeId },
    { enabled: isOpen && !!codeId }
  )

  if (isLoading) {
    return (
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          <ModalBody className="flex justify-center items-center p-8">
            <Spinner size="lg" />
          </ModalBody>
        </ModalContent>
      </Modal>
    )
  }

  if (!codeData) {
    return null
  }

  const getStatusChip = (isActive: boolean) => {
    return (
      <Chip color={isActive ? "success" : "danger"} variant="flat">
        {isActive ? "Actif" : "Inactif"}
      </Chip>
    )
  }

  const getEffectTypeChip = (effectType: string) => {
    const colors = {
      MONTHLY_ONLY: "primary",
      ANNUAL_ONLY: "secondary",
      BOTH: "success",
    } as const

    const labels = {
      MONTHLY_ONLY: "Mensuel uniquement",
      ANNUAL_ONLY: "Annuel uniquement",
      BOTH: "Les deux",
    }

    return (
      <Chip color={colors[effectType as keyof typeof colors]} variant="flat">
        {labels[effectType as keyof typeof labels]}
      </Chip>
    )
  }

  const getDiscountTypeChip = (discountType: string) => {
    return (
      <Chip color="default" variant="flat">
        {discountType === "PERCENTAGE" ? "Pourcentage" : "Montant fixe"}
      </Chip>
    )
  }

  const getDiscountDisplay = (discountType: string, discountValue: number) => {
    return discountType === "PERCENTAGE" ? `${discountValue}%` : `${discountValue / 100}€`
  }

  const formatDate = (date: Date | null) => {
    if (!date) return "Non défini"
    return format(new Date(date), "dd MMMM yyyy", { locale: fr })
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl" scrollBehavior="inside">
      <ModalContent>
        <ModalHeader>
          <div>
            <h2 className="text-xl font-semibold">Détails du code promotionnel</h2>
            <div className="flex items-center gap-2 mt-1">
              <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm">
                {codeData.code}
              </code>
              {getStatusChip(codeData.isActive)}
            </div>
          </div>
        </ModalHeader>
        <ModalBody className="space-y-4">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium">Informations générales</h3>
            </CardHeader>
            <CardBody className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-600">Nom</p>
                <p>{codeData.name}</p>
              </div>
              {codeData.description && (
                <div>
                  <p className="text-sm font-medium text-gray-600">Description</p>
                  <p>{codeData.description}</p>
                </div>
              )}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">Type d'effet</p>
                  {getEffectTypeChip(codeData.effectType)}
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Type de remise</p>
                  {getDiscountTypeChip(codeData.discountType)}
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium">Configuration de la remise</h3>
            </CardHeader>
            <CardBody className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">Valeur de la remise</p>
                  <p className="text-lg font-semibold text-primary">
                    {getDiscountDisplay(codeData.discountType, codeData.discountValue)}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Nombre de paiements</p>
                  <p className="text-lg font-semibold">{codeData.paymentCount}</p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium">Période de validité</h3>
            </CardHeader>
            <CardBody className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">Date de début</p>
                  <p>{formatDate(codeData.validFrom)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Date de fin</p>
                  <p>{formatDate(codeData.validUntil)}</p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium">Utilisation</h3>
            </CardHeader>
            <CardBody className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">Utilisations actuelles</p>
                  <p className="text-lg font-semibold">{codeData.currentUsageCount}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Limite d'utilisation</p>
                  <p className="text-lg font-semibold">
                    {codeData.maxUsageCount || "Illimitée"}
                  </p>
                </div>
              </div>
              {codeData.maxUsageCount && (
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full"
                    style={{
                      width: `${Math.min((codeData.currentUsageCount / codeData.maxUsageCount) * 100, 100)}%`,
                    }}
                  />
                </div>
              )}
            </CardBody>
          </Card>

          {codeData.restrictedToPlans.length > 0 && (
            <Card>
              <CardHeader>
                <h3 className="text-lg font-medium">Plans autorisés</h3>
              </CardHeader>
              <CardBody>
                <div className="flex flex-wrap gap-2">
                  {codeData.restrictedToPlans.map((plan) => (
                    <Chip key={plan.id} color="primary" variant="flat">
                      {plan.name}
                    </Chip>
                  ))}
                </div>
              </CardBody>
            </Card>
          )}

          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium">Métadonnées</h3>
            </CardHeader>
            <CardBody className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">Créé le</p>
                  <p>{formatDate(codeData.createdAt)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Modifié le</p>
                  <p>{formatDate(codeData.updatedAt)}</p>
                </div>
              </div>
            </CardBody>
          </Card>
        </ModalBody>
        <ModalFooter>
          <Button onPress={onClose}>
            Fermer
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
