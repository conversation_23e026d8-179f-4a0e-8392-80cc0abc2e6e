import { Metadata } from "next"

import { getDictionary } from "@/lib/langs"
import { Locale } from "@/lib/i18n-config"

import PromotionalCodesManagement from "./components/promotional-codes-management"

export const metadata: Metadata = {
  title: "Gestion des codes promotionnels - Administration",
  description: "Interface d'administration pour gérer les codes promotionnels",
}

export default async function PromotionalCodesPage({
  params: { lang },
}: {
  params: { lang: Locale }
}) {
  const dictionary = await getDictionary(lang, {
    admin: {
      promotionalCodes: {
        title: true,
        description: true,
        createNew: true,
        searchPlaceholder: true,
        noCodesFound: true,
        loading: true,
        error: true,
      },
    },
  })

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          {dictionary.admin.promotionalCodes.title}
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          {dictionary.admin.promotionalCodes.description}
        </p>
      </div>

      <PromotionalCodesManagement dictionary={dictionary} />
    </div>
  )
}
