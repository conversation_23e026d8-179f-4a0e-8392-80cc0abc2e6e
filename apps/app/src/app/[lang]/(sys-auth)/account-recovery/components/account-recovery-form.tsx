"use client"

import React, { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON>ertCircle, CheckCircle2, RefreshCw } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { authRoutes } from "@/constants/auth"
import { TDictionary } from "@/lib/langs"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Session } from "next-auth"

interface AccountRecoveryFormProps {
  dictionary: TDictionary<{
    accountRecovery: {
      title: true
      description: true
      recoverButton: true
      backToLogin: true
      success: true
      error: true
    }
    errors: {
      accountNotFrozen: true
      gracePeriodExpired: true
      userNotFound: true
    }
    unknownError: true
  }>
  session: Session | null
}

export default function AccountRecoveryForm({ dictionary, session }: AccountRecoveryFormProps) {
  const router = useRouter()
  const [isRecovering, setIsRecovering] = useState(false)
  const [recoveryStatus, setRecoveryStatus] = useState<"idle" | "success" | "error">("idle")
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  const recoverAccountMutation = trpc.me.recoverFrozenAccount.useMutation({
    onSuccess: () => {
      setRecoveryStatus("success")
      setIsRecovering(false)
      toast.success(dictionary.accountRecovery.success)
      
      // Redirect to home page after a short delay
      setTimeout(() => {
        router.push("/")
      }, 2000)
    },
    onError: (error) => {
      setRecoveryStatus("error")
      setIsRecovering(false)
      
      let errorMsg = dictionary.unknownError
      
      if (error.message.includes("accountNotFrozen")) {
        errorMsg = dictionary.errors.accountNotFrozen
      } else if (error.message.includes("gracePeriodExpired")) {
        errorMsg = dictionary.errors.gracePeriodExpired
      } else if (error.message.includes("userNotFound")) {
        errorMsg = dictionary.errors.userNotFound
      }
      
      setErrorMessage(errorMsg)
      toast.error(errorMsg)
    },
  })

  const handleRecoverAccount = () => {
    if (!session?.user) {
      setErrorMessage("Vous devez être connecté pour récupérer votre compte")
      return
    }

    setIsRecovering(true)
    setRecoveryStatus("idle")
    setErrorMessage(null)
    
    recoverAccountMutation.mutate()
  }

  if (recoveryStatus === "success") {
    return (
      <Card className="w-full">
        <CardBody className="text-center space-y-4">
          <CheckCircle2 className="mx-auto h-12 w-12 text-success" />
          <div>
            <h3 className="text-lg font-semibold text-success">Compte récupéré avec succès!</h3>
            <p className="text-sm text-muted-foreground mt-2">
              Votre compte a été réactivé. Vous allez être redirigé automatiquement.
            </p>
          </div>
        </CardBody>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <h2 className="text-xl font-semibold">Récupération de compte</h2>
      </CardHeader>
      <CardBody className="space-y-4">
        {session?.user ? (
          <>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Compte: <span className="font-medium">{session.user.email}</span>
              </p>
              <p className="text-sm text-muted-foreground">
                Votre compte a été gelé suite à une demande de suppression. 
                Vous pouvez le récupérer en cliquant sur le bouton ci-dessous.
              </p>
            </div>

            {errorMessage && (
              <div className="flex items-center gap-2 rounded-lg border border-danger-200 bg-danger-50 p-3">
                <AlertCircle className="h-5 w-5 shrink-0 text-danger" />
                <p className="text-sm text-danger">{errorMessage}</p>
              </div>
            )}

            <div className="space-y-3">
              <Button
                color="primary"
                className="w-full"
                onPress={handleRecoverAccount}
                isLoading={isRecovering}
                startContent={!isRecovering ? <RefreshCw className="h-4 w-4" /> : undefined}
              >
                {isRecovering ? "Récupération en cours..." : dictionary.accountRecovery.recoverButton}
              </Button>
              
              <Button
                variant="light"
                className="w-full"
                as={Link}
                href={authRoutes.signIn[0]}
                isDisabled={isRecovering}
              >
                {dictionary.accountRecovery.backToLogin}
              </Button>
            </div>
          </>
        ) : (
          <div className="text-center space-y-4">
            <div className="flex items-center gap-2 rounded-lg border border-warning-200 bg-warning-50 p-3">
              <AlertCircle className="h-5 w-5 shrink-0 text-warning" />
              <p className="text-sm text-warning">
                Vous devez être connecté pour récupérer votre compte.
              </p>
            </div>
            
            <Button
              color="primary"
              as={Link}
              href={authRoutes.signIn[0]}
              className="w-full"
            >
              Se connecter
            </Button>
          </div>
        )}
      </CardBody>
    </Card>
  )
}
