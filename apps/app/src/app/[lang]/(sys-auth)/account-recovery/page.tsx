import { Metada<PERSON> } from "next"
import { redirect } from "next/navigation"

import { auth } from "@/lib/auth"
import { Locale } from "@/lib/i18n-config"
import { getDictionary } from "@/lib/langs"

import AccountRecoveryForm from "./components/account-recovery-form"

export const metadata: Metadata = {
  title: "Récupération de compte",
  description: "Récupérez votre compte gelé",
}

export default async function AccountRecoveryPage({
  params: { lang },
}: {
  params: {
    lang: Locale
  }
}) {
  const session = await auth()
  
  // If user is already logged in and account is not frozen, redirect
  if (session?.user && !session.user.isFrozen) {
    redirect("/")
  }

  const dictionary = await getDictionary(lang, {
    accountRecovery: {
      title: true,
      description: true,
      recoverButton: true,
      backToLogin: true,
      success: true,
      error: true,
    },
    errors: {
      accountNotFrozen: true,
      gracePeriodExpired: true,
      userNotFound: true,
    },
    unknownError: true,
  })

  return (
    <main className="container relative m-auto flex min-h-screen flex-1 flex-col items-center justify-center px-2 lg:max-w-none lg:px-0">
      <div className="w-full max-w-md space-y-6">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">
            {dictionary.accountRecovery.title}
          </h1>
          <p className="text-sm text-muted-foreground">
            {dictionary.accountRecovery.description}
          </p>
        </div>
        
        <AccountRecoveryForm dictionary={dictionary} session={session} />
      </div>
    </main>
  )
}
