import { NextRequest, NextResponse } from "next/server"

import { env } from "@/lib/env"
import { runFreeTrialJobs } from "@/lib/cron/free-trial-jobs"
import { logger } from "@coheadcoaching/lib"

export async function POST(request: NextRequest) {
  try {
    // Verify the cron secret to ensure this is called by the cron service
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${env.CRON_SECRET}`
    
    if (!authHeader || authHeader !== expectedAuth) {
      logger.warn("Unauthorized cron request", { authHeader })
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    logger.log("Starting free trial cron job")
    
    await runFreeTrialJobs()
    
    logger.log("Free trial cron job completed successfully")
    
    return NextResponse.json({ 
      success: true, 
      message: "Free trial jobs completed successfully",
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    logger.error("Free trial cron job failed", error)
    
    return NextResponse.json(
      { 
        error: "Free trial cron job failed", 
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString()
      }, 
      { status: 500 }
    )
  }
}

// Also support GET for testing purposes (only in development)
export async function GET(request: NextRequest) {
  if (env.NODE_ENV !== "development") {
    return NextResponse.json({ error: "Not allowed in production" }, { status: 403 })
  }
  
  return POST(request)
}
