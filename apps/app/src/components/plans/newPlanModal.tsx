"use client"

import React, { useEffect, useState } from "react"
import { Plus } from "lucide-react"
import { toast } from "react-toastify"
import { z } from "zod"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Checkbox } from "@nextui-org/checkbox"
import { Input, Textarea } from "@nextui-org/input"
import { <PERSON>dal, ModalBody, <PERSON>dal<PERSON>ontent, <PERSON><PERSON>ooter, ModalHeader } from "@nextui-org/modal"

const featureSchema = z.object({
  text: z.string(),
  included: z.boolean(),
})

const planSchema = z.object({
  name: z.string().min(1, "Le nom est requis"),
  description: z.string().optional(),
  monthlyPrice: z.number().positive("Le prix doit être un nombre positif"),
  annualPrice: z.number().positive("Le prix doit être un nombre positif"),
  monthlyRefundPercentage: z.number().int().min(0).max(100).optional(),
  annualRefundPercentage: z.number().int().min(0).max(100).optional(),
  freeTrialDays: z.number().int().min(0).max(365).optional(),
  features: z.array(featureSchema),
  isRecommended: z.boolean(),
  isActive: z.boolean(),
})

interface NewPlanModalProps {
  onSuccess: () => void
}

// Ajout des types de restrictions
type Restriction = {
  type: "MAX_MESSAGES_PER_CHAT" | "MAX_SAVED_CHATS" | "MAX_AGENTS" | "MAX_CATEGORIES"
  value: number | null
  label: string
}

const NewPlanModal: React.FC<NewPlanModalProps> = ({ onSuccess }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    monthlyPrice: 0,
    annualPrice: 0,
    monthlyRefundPercentage: 0,
    annualRefundPercentage: 0,
    freeTrialDays: 0,
    features: [{ text: "", included: true }],
    isRecommended: false,
    isActive: true,
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [priceInputs, setPriceInputs] = useState({
    monthlyPrice: "",
    annualPrice: "",
    monthlyRefundPercentage: "",
    annualRefundPercentage: "",
    freeTrialDays: "",
  })
  const [restrictions, setRestrictions] = useState<Restriction[]>([
    { type: "MAX_MESSAGES_PER_CHAT", value: null, label: "Nombre maximum de messages par conversation" },
    { type: "MAX_SAVED_CHATS", value: null, label: "Nombre maximum de conversations enregistrées" },
    { type: "MAX_AGENTS", value: null, label: "Nombre maximum d'agents accessibles" },
    { type: "MAX_CATEGORIES", value: null, label: "Nombre maximum de catégories sélectionnables" },
  ])

  const createPlan = trpc.plan.create.useMutation({
    onSuccess: () => {
      toast.success("Plan créé avec succès!")
      setIsOpen(false)
      resetForm()
      onSuccess()
    },
    onError: (error) => {
      toast.error(`Erreur lors de la création du plan: ${error.message}`)
    },
  })

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      monthlyPrice: 0,
      annualPrice: 0,
      monthlyRefundPercentage: 0,
      annualRefundPercentage: 0,
      features: [{ text: "", included: true }],
      isRecommended: false,
      isActive: true,
    })
    setErrors({})
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }))
    }
  }

  const handlePriceInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    // Allow user to type normally
    setPriceInputs((prev) => ({ ...prev, [name]: value }))

    // Only convert to cents when value is valid
    if (value === "" || value === "0") {
      setFormData((prev) => ({ ...prev, [name]: 0 }))
    } else {
      const floatValue = parseFloat(value)
      if (!isNaN(floatValue)) {
        const priceInCents = Math.round(floatValue * 100)
        setFormData((prev) => ({ ...prev, [name]: priceInCents }))
      }
    }

    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }))
    }
  }

  const handlePercentageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    // Allow user to type normally
    setPriceInputs((prev) => ({ ...prev, [name]: value }))

    // Only convert to integer when value is valid
    if (value === "" || value === "0") {
      setFormData((prev) => ({ ...prev, [name]: 0 }))
    } else {
      const intValue = parseInt(value, 10)
      if (!isNaN(intValue) && intValue >= 0 && intValue <= 100) {
        setFormData((prev) => ({ ...prev, [name]: intValue }))
      }
    }

    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }))
    }
  }

  const handleTrialInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    // Allow user to type normally
    setPriceInputs((prev) => ({ ...prev, [name]: value }))

    // Only convert to integer when value is valid
    if (value === "" || value === "0") {
      setFormData((prev) => ({ ...prev, [name]: 0 }))
    } else {
      const intValue = parseInt(value, 10)
      if (!isNaN(intValue) && intValue >= 0 && intValue <= 365) {
        setFormData((prev) => ({ ...prev, [name]: intValue }))
      }
    }

    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }))
    }
  }

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }))
  }

  const handleFeatureChange = (index: number, value: string) => {
    const newFeatures = [...formData.features]
    newFeatures[index] = { ...newFeatures[index], text: value }
    setFormData((prev) => ({ ...prev, features: newFeatures }))
  }

  const handleFeatureIncludedChange = (index: number, included: boolean) => {
    const newFeatures = [...formData.features]
    newFeatures[index] = { ...newFeatures[index], included }
    setFormData((prev) => ({ ...prev, features: newFeatures }))
  }

  const addFeature = () => {
    setFormData((prev) => ({ ...prev, features: [...prev.features, { text: "", included: true }] }))
  }

  const removeFeature = (index: number) => {
    const newFeatures = formData.features.filter((_, i) => i !== index)
    setFormData((prev) => ({
      ...prev,
      features: newFeatures.length ? newFeatures : [{ text: "", included: true }],
    }))
  }

  const handleRestrictionChange = (type: Restriction["type"], value: string) => {
    setRestrictions((prev) =>
      prev.map((r) => (r.type === type ? { ...r, value: value === "" ? null : parseInt(value, 10) } : r))
    )
  }

  const handleSubmit = () => {
    try {
      // Filter out empty features
      const filteredFeatures = formData.features.filter((f) => f.text.trim() !== "")

      // Préparer les restrictions pour l'API
      const restrictionsData = restrictions.map((r) => ({
        type: r.type,
        value: r.value,
      }))

      const dataToValidate = {
        ...formData,
        features: filteredFeatures,
        restrictions: restrictionsData,
      }

      // Valider les données
      planSchema.parse(dataToValidate)

      // Soumettre les données
      createPlan.mutate(dataToValidate)
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors = error.flatten().fieldErrors
        const errorMessages = Object.keys(fieldErrors).reduce(
          (acc, key) => {
            acc[key] = fieldErrors[key]?.join(", ") || ""
            return acc
          },
          {} as Record<string, string>
        )
        setErrors(errorMessages)
        toast.error("Veuillez corriger les erreurs dans le formulaire")
      }
    }
  }

  useEffect(() => {
    if (isOpen) {
      setPriceInputs({
        monthlyPrice: formData.monthlyPrice > 0 ? (formData.monthlyPrice / 100).toString() : "",
        annualPrice: formData.annualPrice > 0 ? (formData.annualPrice / 100).toString() : "",
        monthlyRefundPercentage:
          formData.monthlyRefundPercentage > 0 ? formData.monthlyRefundPercentage.toString() : "",
        annualRefundPercentage: formData.annualRefundPercentage > 0 ? formData.annualRefundPercentage.toString() : "",
        freeTrialDays: formData.freeTrialDays > 0 ? formData.freeTrialDays.toString() : "",
      })
    }
  }, [
    isOpen,
    formData.annualPrice,
    formData.monthlyPrice,
    formData.monthlyRefundPercentage,
    formData.annualRefundPercentage,
    formData.freeTrialDays,
  ])

  return (
    <>
      <Button color="primary" startContent={<Plus size={18} />} onPress={() => setIsOpen(true)}>
        Ajouter un plan
      </Button>

      <Modal isOpen={isOpen} onClose={() => setIsOpen(false)} size="2xl" scrollBehavior="inside">
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold">Créer un nouveau plan</h2>
            <p className="text-sm text-default-500">Ajoutez un nouveau plan d&apos;abonnement</p>
          </ModalHeader>
          <ModalBody>
            <div className="mb-4 rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
              <h3 className="mb-2 text-sm font-medium">Guide des champs</h3>
              <ul className="space-y-1 text-xs">
                <li>
                  <span className="font-semibold">Nom et Description</span>: Affichés aux utilisateurs (copywriting
                  uniquement)
                </li>
                <li>
                  <span className="font-semibold">Prix</span>: Détermine le montant facturé aux utilisateurs
                </li>
                <li>
                  <span className="font-semibold">Fonctionnalités</span>: Liste affichée aux utilisateurs (copywriting
                  uniquement)
                  <ul className="ml-4 mt-1">
                    <li>✓ = Fonctionnalité incluse dans ce plan</li>
                    <li>✗ = Fonctionnalité non incluse (affichée barrée)</li>
                  </ul>
                </li>
                <li>
                  <span className="font-semibold">Restrictions</span>:{" "}
                  <span className="text-primary-600 dark:text-primary-400">Impact fonctionnel</span> - Limite réellement
                  les capacités des utilisateurs
                </li>
                <li>
                  <span className="font-semibold">Plan recommandé</span>: Met en évidence ce plan dans l&apos;interface
                </li>
                <li>
                  <span className="font-semibold">Plan actif</span>:{" "}
                  <span className="text-primary-600 dark:text-primary-400">Impact fonctionnel</span> - Détermine si le
                  plan est disponible à l&apos;achat
                </li>
              </ul>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <Input
                label="Nom du plan"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Ex: Premium"
                variant="bordered"
                isRequired
                isInvalid={!!errors.name}
                errorMessage={errors.name}
              />

              <Textarea
                label="Description"
                name="description"
                minRows={1}
                maxRows={5}
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Description du plan..."
                variant="bordered"
              />

              <Input
                label="Prix mensuel (€)"
                name="monthlyPrice"
                type="text"
                value={priceInputs.monthlyPrice}
                onChange={handlePriceInputChange}
                placeholder="Ex: 9.99"
                variant="bordered"
                isRequired
                isInvalid={!!errors.monthlyPrice}
                errorMessage={errors.monthlyPrice}
                startContent={<div className="pointer-events-none flex items-center">€</div>}
              />

              <Input
                label="Prix annuel (€)"
                name="annualPrice"
                type="text"
                value={priceInputs.annualPrice}
                onChange={handlePriceInputChange}
                placeholder="Ex: 99.99"
                variant="bordered"
                isRequired
                isInvalid={!!errors.annualPrice}
                errorMessage={errors.annualPrice}
                startContent={<div className="pointer-events-none flex items-center">€</div>}
              />

              <Input
                label="Remboursement mensuel (%)"
                name="monthlyRefundPercentage"
                type="number"
                min="0"
                max="100"
                value={priceInputs.monthlyRefundPercentage}
                onChange={handlePercentageInputChange}
                placeholder="Ex: 20 (optionnel)"
                variant="bordered"
                isInvalid={!!errors.monthlyRefundPercentage}
                errorMessage={errors.monthlyRefundPercentage}
                endContent={<div className="pointer-events-none flex items-center">%</div>}
                description="Pourcentage du montant net reçu (après frais) remboursé pour l'abonnement mensuel"
              />

              <Input
                label="Remboursement annuel (%)"
                name="annualRefundPercentage"
                type="number"
                min="0"
                max="100"
                value={priceInputs.annualRefundPercentage}
                onChange={handlePercentageInputChange}
                placeholder="Ex: 30 (optionnel)"
                variant="bordered"
                isInvalid={!!errors.annualRefundPercentage}
                errorMessage={errors.annualRefundPercentage}
                endContent={<div className="pointer-events-none flex items-center">%</div>}
                description="Pourcentage du montant net reçu (après frais) remboursé pour l'abonnement annuel"
              />

              <Input
                label="Essai gratuit (jours)"
                name="freeTrialDays"
                type="number"
                min="0"
                max="365"
                value={priceInputs.freeTrialDays}
                onChange={handleTrialInputChange}
                placeholder="Ex: 7 (optionnel)"
                variant="bordered"
                isInvalid={!!errors.freeTrialDays}
                errorMessage={errors.freeTrialDays}
                endContent={<div className="pointer-events-none flex items-center">jours</div>}
                description="Nombre de jours d'essai gratuit offerts aux nouveaux utilisateurs (0 = pas d'essai)"
              />
            </div>

            <div className="mt-4">
              <label className="mb-2 block text-sm font-medium">Fonctionnalités</label>
              <p className="mb-2 text-xs text-default-500">
                Les cases à cocher indiquent si la fonctionnalité est incluse (✓) ou non (✗) dans ce plan.
              </p>
              {formData.features.map((feature, index) => (
                <div key={index} className="mb-2 flex items-center gap-2">
                  <Checkbox
                    isSelected={feature.included}
                    onValueChange={(checked) => handleFeatureIncludedChange(index, checked)}
                    size="sm"
                    color="success"
                  />
                  <Input
                    value={feature.text}
                    onChange={(e) => handleFeatureChange(index, e.target.value)}
                    placeholder="Ex: Accès illimité aux agents"
                    variant="bordered"
                    className="grow"
                  />
                  <Button
                    isIconOnly
                    color="danger"
                    variant="light"
                    onPress={() => removeFeature(index)}
                    disabled={formData.features.length === 1 && !feature.text}
                  >
                    &times;
                  </Button>
                </div>
              ))}
              <Button variant="flat" size="sm" onPress={addFeature} className="mt-2">
                + Ajouter une fonctionnalité
              </Button>
            </div>

            <div className="mt-4">
              <label className="mb-2 block text-sm font-medium">
                Restrictions{" "}
                <span className="text-xs text-primary-600 dark:text-primary-400">(impact fonctionnel)</span>
              </label>
              {restrictions.map((restriction) => (
                <div key={restriction.type} className="mb-4 flex flex-col gap-1">
                  <Input
                    type="number"
                    min="0"
                    label={restriction.label}
                    value={restriction.value === null ? "" : restriction.value.toString()}
                    onChange={(e) => handleRestrictionChange(restriction.type, e.target.value)}
                    placeholder="Illimité"
                    variant="bordered"
                    className="grow"
                    description={
                      restriction.type === "MAX_MESSAGES_PER_CHAT"
                        ? "Limite le nombre de messages par conversation. Laisser vide pour illimité."
                        : restriction.type === "MAX_SAVED_CHATS"
                          ? "Limite le nombre de conversations sauvegardées. Laisser vide pour illimité."
                          : restriction.type === "MAX_AGENTS"
                            ? "Limite le nombre d'agents accessibles. Laisser vide pour illimité."
                            : "Limite le nombre de catégories sélectionnables. Laisser vide pour illimité."
                    }
                  />
                </div>
              ))}
            </div>

            <div className="mt-4 flex flex-col gap-4">
              <Checkbox
                isSelected={formData.isRecommended}
                onValueChange={(checked) => handleCheckboxChange("isRecommended", checked)}
              >
                Plan recommandé
              </Checkbox>

              <Checkbox
                isSelected={formData.isActive}
                onValueChange={(checked) => handleCheckboxChange("isActive", checked)}
              >
                Plan actif
              </Checkbox>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onPress={() => setIsOpen(false)}>
              Annuler
            </Button>
            <Button color="primary" onPress={handleSubmit} isLoading={createPlan.isPending}>
              Créer le plan
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}

export default NewPlanModal
