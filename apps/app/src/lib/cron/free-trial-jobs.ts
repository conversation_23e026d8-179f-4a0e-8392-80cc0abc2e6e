import { logger } from "@coheadcoaching/lib"

import { sendFreeTrialReminderEmail } from "../email/free-trial-reminder"
import { getExpiredTrials, getTrialsNeedingReminders, markTrialReminderSent, processTrialExpiration } from "../free-trial"

/**
 * Cron job to send free trial reminder emails
 * Should be run daily
 */
export async function sendFreeTrialReminders() {
  logger.log("Starting free trial reminder job")

  try {
    const subscriptions = await getTrialsNeedingReminders()
    
    logger.log(`Found ${subscriptions.length} trials needing reminders`)

    for (const subscription of subscriptions) {
      try {
        if (!subscription.user.email) {
          logger.warn("User has no email address", { userId: subscription.userId })
          continue
        }

        // Send reminder email
        await sendFreeTrialReminderEmail({
          email: subscription.user.email,
          userName: subscription.user.username || subscription.user.name || "User",
          planName: subscription.plan.name,
          trialEndDate: subscription.freeTrialEndDate!,
          subscriptionId: subscription.id,
        })

        // Mark reminder as sent
        await markTrialReminderSent(subscription.id)

        logger.log("Free trial reminder sent", {
          subscriptionId: subscription.id,
          userEmail: subscription.user.email,
        })
      } catch (error) {
        logger.error("Failed to send free trial reminder", {
          subscriptionId: subscription.id,
          error,
        })
      }
    }

    logger.log("Free trial reminder job completed", { processed: subscriptions.length })
  } catch (error) {
    logger.error("Free trial reminder job failed", error)
    throw error
  }
}

/**
 * Cron job to process expired free trials
 * Should be run daily
 */
export async function processExpiredFreeTrials() {
  logger.log("Starting expired free trials processing job")

  try {
    const subscriptions = await getExpiredTrials()
    
    logger.log(`Found ${subscriptions.length} expired trials to process`)

    for (const subscription of subscriptions) {
      try {
        await processTrialExpiration(subscription.id)

        logger.log("Expired trial processed successfully", {
          subscriptionId: subscription.id,
          userId: subscription.userId,
        })
      } catch (error) {
        logger.error("Failed to process expired trial", {
          subscriptionId: subscription.id,
          error,
        })
      }
    }

    logger.log("Expired free trials processing job completed", { processed: subscriptions.length })
  } catch (error) {
    logger.error("Expired free trials processing job failed", error)
    throw error
  }
}

/**
 * Combined job that runs both reminder and expiration processing
 * This can be called from a single cron endpoint
 */
export async function runFreeTrialJobs() {
  logger.log("Starting combined free trial jobs")

  try {
    // Run reminder job first
    await sendFreeTrialReminders()
    
    // Then process expirations
    await processExpiredFreeTrials()

    logger.log("Combined free trial jobs completed successfully")
  } catch (error) {
    logger.error("Combined free trial jobs failed", error)
    throw error
  }
}
