import { ACCOUNT_FREEZE_GRACE_PERIOD_DAYS } from "@/constants"
import { env } from "@/lib/env"
import { sendMail } from "@/lib/mailer"
import { logger } from "@coheadcoaching/lib"
import AccountRecoveryEmail from "@coheadcoaching/transactional/emails/account-recovery"
import { render } from "@react-email/render"

export interface SendAccountRecoveryEmailParams {
  email: string
  userName: string
  userId: string
}

export async function sendAccountRecoveryEmail(params: SendAccountRecoveryEmailParams) {
  const { email, userName, userId } = params

  try {
    const recoveryUrl = `${env.NEXT_PUBLIC_BASE_URL}/account-recovery`
    
    const emailHtml = render(
      AccountRecoveryEmail({
        userName,
        recoveryUrl,
        gracePeriodDays: ACCOUNT_FREEZE_GRACE_PERIOD_DAYS,
        logoUrl: `${env.NEXT_PUBLIC_BASE_URL}/logo.svg`,
      })
    )

    await sendMail({
      to: email,
      subject: "Récupération de votre compte CoheadCoaching",
      html: emailHtml,
    })

    logger.log("Account recovery email sent successfully", {
      email,
      userId,
    })
  } catch (error) {
    logger.error("Failed to send account recovery email", {
      email,
      userId,
      error,
    })
    throw error
  }
}
