import { format } from "date-fns"
import { fr } from "date-fns/locale"

import { env } from "@/lib/env"
import { sendMail } from "@/lib/mailer"
import { logger } from "@coheadcoaching/lib"
import FreeTrialReminderEmail from "@coheadcoaching/transactional/emails/free-trial-reminder"
import { render } from "@react-email/render"

export interface SendFreeTrialReminderEmailParams {
  email: string
  userName: string
  planName: string
  trialEndDate: Date
  subscriptionId: string
}

export async function sendFreeTrialReminderEmail(params: SendFreeTrialReminderEmailParams) {
  const { email, userName, planName, trialEndDate, subscriptionId } = params

  try {
    const formattedEndDate = format(trialEndDate, "dd MMMM yyyy", { locale: fr })
    
    const emailHtml = render(
      FreeTrialReminderEmail({
        userName,
        planName,
        trialEndDate: formattedEndDate,
        manageSubscriptionUrl: `${env.NEXT_PUBLIC_BASE_URL}/profile/subscription`,
        logoUrl: `${env.NEXT_PUBLIC_BASE_URL}/logo.svg`,
      })
    )

    await sendMail({
      to: email,
      subject: `Votre essai gratuit se termine bientôt - ${planName}`,
      html: emailHtml,
    })

    logger.log("Free trial reminder email sent successfully", {
      email,
      subscriptionId,
      trialEndDate: formattedEndDate,
    })
  } catch (error) {
    logger.error("Failed to send free trial reminder email", {
      email,
      subscriptionId,
      error,
    })
    throw error
  }
}
