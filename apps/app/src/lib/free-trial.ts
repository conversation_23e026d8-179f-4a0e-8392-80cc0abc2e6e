import { addDays, isBefore } from "date-fns"

import { FREE_TRIAL_REMINDER_DAYS_BEFORE } from "@/constants"
import { prisma } from "@/lib/prisma"
import { logger } from "@coheadcoaching/lib"
import { BillingPeriod, SubscriptionStatus } from "@prisma/client"

import { getBrowserInfo } from "./utils/browser-info"
import { env } from "./env"
import { createRecurringPayment, initiateRecurringPayment } from "./mangopay"

export interface CreateFreeTrialSubscriptionParams {
  userId: string
  planId: number
  billingPeriod: BillingPeriod
  browserInfos: ReturnType<typeof getBrowserInfo>
  IP: string
  mangopayCardId: string
  promotionalCodeId?: string
}

export async function createFreeTrialSubscription(params: CreateFreeTrialSubscriptionParams) {
  const { userId, planId, billingPeriod, mangopayCardId, promotionalCodeId } = params

  logger.log("Starting createFreeTrialSubscription", { userId, planId, billingPeriod })

  // 1. Fetch necessary data
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { cards: { where: { mangopayCardId: mangopayCardId, isTemp: false } } },
  })

  if (!user) {
    throw new Error("User not found")
  }

  if (user.hasUsedFreeTrial) {
    throw new Error("User has already used their free trial")
  }

  const plan = await prisma.plan.findUnique({
    where: { id: planId },
  })

  if (!plan) {
    throw new Error("Plan not found")
  }

  if (!plan.freeTrialDays) {
    throw new Error("Plan does not offer free trial")
  }

  const card = user.cards[0]
  if (!card) {
    throw new Error("Card not found")
  }

  // 2. Calculate trial dates
  const startDate = new Date()
  const freeTrialEndDate = addDays(startDate, plan.freeTrialDays)
  const endDate = billingPeriod === "MONTHLY" ? addDays(freeTrialEndDate, 30) : addDays(freeTrialEndDate, 365)

  // 3. Create €0 CIT (Customer Initiated Transaction) for payment method validation
  const amount = 0 // €0 for validation
  const nextAmount = billingPeriod === "MONTHLY" ? plan.monthlyPrice * 100 : plan.annualPrice * 100 // Convert to cents

  try {
    // Create recurring payment registration with €0 first payment
    const recurringRegistration = await createRecurringPayment({
      authorId: user.mangopayUserId!,
      creditedUserId: user.mangopayUserId!,
      creditedWalletId: user.mangopayWalletId!,
      cardId: card.mangopayCardId!,
      firstTransactionAmount: amount,
      nextTransactionAmount: nextAmount,
      currency: "EUR",
      frequency: billingPeriod === "MONTHLY" ? "Monthly" : "Annual",
      tag: `free-trial-${userId}-${planId}`,
    })

    // 4. Create subscription in database
    const subscription = await prisma.subscription.create({
      data: {
        userId,
        planId,
        status: SubscriptionStatus.ACTIVE,
        billingPeriod,
        startDate,
        endDate,
        isFreeTrial: true,
        freeTrialEndDate,
        trialReminderSent: false,
        mangopayRecurringRegistrationId: recurringRegistration.Id,
        promotionalCodeId,
      },
    })

    // 5. Create initial €0 payment record
    await prisma.payment.create({
      data: {
        subscriptionId: subscription.id,
        amount: 0,
        currency: "EUR",
        status: "SUCCEEDED",
        mangopayPayinId: null, // No actual payment for €0
      },
    })

    // 6. Mark user as having used free trial
    await prisma.user.update({
      where: { id: userId },
      data: { hasUsedFreeTrial: true },
    })

    // 7. Record promotional code usage if applicable
    if (promotionalCodeId) {
      await recordPromotionalCodeUsage(userId, promotionalCodeId, subscription.id)
    }

    logger.log("Free trial subscription created successfully", {
      subscriptionId: subscription.id,
      freeTrialEndDate,
      recurringRegistrationId: recurringRegistration.Id,
    })

    return subscription
  } catch (error) {
    logger.error("Failed to create free trial subscription", error)
    throw error
  }
}

export async function processTrialExpiration(subscriptionId: string) {
  logger.log("Processing trial expiration", { subscriptionId })

  const subscription = await prisma.subscription.findUnique({
    where: { id: subscriptionId },
    include: {
      user: true,
      plan: true,
    },
  })

  if (!subscription) {
    throw new Error("Subscription not found")
  }

  if (!subscription.isFreeTrial || !subscription.freeTrialEndDate) {
    throw new Error("Subscription is not a free trial")
  }

  const now = new Date()
  if (isBefore(now, subscription.freeTrialEndDate)) {
    throw new Error("Trial has not expired yet")
  }

  try {
    // Initiate the first real payment
    if (subscription.mangopayRecurringRegistrationId) {
      const amount =
        subscription.billingPeriod === "MONTHLY" ? subscription.plan.monthlyPrice : subscription.plan.annualPrice
      const secureModeReturnURL = `${env.NEXT_PUBLIC_BASE_URL}/subscription/return?subscriptionId=${subscriptionId}&trial=true`

      const paymentResult = await initiateRecurringPayment({
        recurringPayinRegistrationId: subscription.mangopayRecurringRegistrationId,
        tag: `CoheadCoaching Trial Expiration - Sub: ${subscriptionId}`,
        amount,
        currency: "EUR",
        secureModeReturnURL,
        statementDescriptor: `ABO ${subscription.plan.name.substring(0, 6)}`,
        browserInfo: {
          AcceptHeader: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
          JavaEnabled: false,
          JavascriptEnabled: true,
          Language: "fr-FR",
          ColorDepth: 24,
          ScreenHeight: 1080,
          ScreenWidth: 1920,
          TimeZoneOffset: -60,
          UserAgent: "Mozilla/5.0 (Server)",
        },
        ipAddress: "0.0.0.0",
      })

      // Create payment record
      await prisma.payment.create({
        data: {
          subscriptionId: subscription.id,
          amount:
            subscription.billingPeriod === "MONTHLY" ? subscription.plan.monthlyPrice : subscription.plan.annualPrice,
          currency: "EUR",
          status: paymentResult.Status === "SUCCEEDED" ? "SUCCEEDED" : "PENDING",
          mangopayPayinId: paymentResult.Id,
        },
      })

      // Update subscription to mark trial as completed
      await prisma.subscription.update({
        where: { id: subscriptionId },
        data: {
          isFreeTrial: false,
          freeTrialEndDate: null,
        },
      })

      logger.log("Trial expiration processed successfully", {
        subscriptionId,
        paymentId: paymentResult.Id,
        status: paymentResult.Status,
      })
    }
  } catch (error) {
    logger.error("Failed to process trial expiration", error)

    // Mark subscription as failed if payment fails
    await prisma.subscription.update({
      where: { id: subscriptionId },
      data: {
        status: SubscriptionStatus.FAILED,
      },
    })

    throw error
  }
}

export async function getTrialsNeedingReminders() {
  const reminderDate = addDays(new Date(), FREE_TRIAL_REMINDER_DAYS_BEFORE)

  const subscriptions = await prisma.subscription.findMany({
    where: {
      isFreeTrial: true,
      trialReminderSent: false,
      freeTrialEndDate: {
        lte: reminderDate,
      },
      status: SubscriptionStatus.ACTIVE,
    },
    include: {
      user: true,
      plan: true,
    },
  })

  return subscriptions
}

export async function markTrialReminderSent(subscriptionId: string) {
  await prisma.subscription.update({
    where: { id: subscriptionId },
    data: { trialReminderSent: true },
  })
}

export async function getExpiredTrials() {
  const now = new Date()

  const subscriptions = await prisma.subscription.findMany({
    where: {
      isFreeTrial: true,
      freeTrialEndDate: {
        lt: now,
      },
      status: SubscriptionStatus.ACTIVE,
    },
    include: {
      user: true,
      plan: true,
    },
  })

  return subscriptions
}

async function recordPromotionalCodeUsage(userId: string, promotionalCodeId: string, subscriptionId: string) {
  try {
    // Check if user has already used this code
    const existingUsage = await prisma.promotionalCodeUsage.findUnique({
      where: {
        userId_promotionalCodeId: {
          userId,
          promotionalCodeId,
        },
      },
    })

    if (existingUsage) {
      throw new Error("User has already used this promotional code")
    }

    // Record usage
    await prisma.promotionalCodeUsage.create({
      data: {
        userId,
        promotionalCodeId,
        subscriptionId,
      },
    })

    // Increment usage count
    await prisma.promotionalCode.update({
      where: { id: promotionalCodeId },
      data: {
        currentUsageCount: {
          increment: 1,
        },
      },
    })

    logger.log("Promotional code usage recorded", { userId, promotionalCodeId, subscriptionId })
  } catch (error) {
    logger.error("Failed to record promotional code usage", error)
    throw error
  }
}
