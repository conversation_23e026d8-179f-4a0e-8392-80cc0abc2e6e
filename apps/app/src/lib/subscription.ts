import { addMonths, addYears } from "date-fns"

import {
  cancelRecurringPaymentRegistration,
  createRecurringPayment,
  getMangoPayUser,
  getPayInDetails,
  getRecurringRegistrationDetails,
  initiateRecurringPayment,
  refundPayin,
} from "@/lib/mangopay"
import { prisma } from "@/lib/prisma"
import { MangopayRecurringPayIn, MangopayUser } from "@/types/mangopay"
import { logger } from "@coheadcoaching/lib"
import { BillingPeriod, PaymentStatus, RefundStatus, SubscriptionStatus } from "@prisma/client"

import { getBrowserInfo } from "./utils/browser-info"
import { env } from "./env"
import { createFreeTrialSubscription } from "./free-trial"

export async function checkUserMangopaySetup(userId: string) {
  // Récupérer l'utilisateur avec ses informations MangoPay
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      cards: {
        where: { isTemp: false },
        select: {
          id: true,
          mangopayCardId: true,
          last4: true,
          expirationDate: true,
          isDefault: true,
        },
      },
    },
  })

  if (!user) {
    throw new Error("Utilisateur non trouvé")
  }

  // Vérifier si l'utilisateur a un compte MangoPay
  const hasMangopayAccount = !!user.mangopayUserId

  // Vérifier si l'utilisateur a au moins une carte enregistrée
  const hasCard = user.cards.length > 0

  return {
    hasMangopayAccount,
    hasCard,
    user: {
      id: user.id,
      email: user.email,
      name: user.name,
      mangopayId: user.mangopayUserId,
      cards: user.cards,
    },
  }
}

export type CheckMangopaySetupType = Awaited<ReturnType<typeof checkUserMangopaySetup>>

export async function createSubscription({
  userId,
  planId,
  billingPeriod,
  browserInfos,
  IP,
  mangopayCardId,
  promotionalCodeId,
}: {
  userId: string
  planId: number
  billingPeriod: BillingPeriod
  browserInfos: ReturnType<typeof getBrowserInfo>
  IP: string
  mangopayCardId: string
  promotionalCodeId?: string
}) {
  logger.log("Starting createSubscription", { userId, planId, billingPeriod, mangopayCardId })

  // 1. Fetch necessary data
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { cards: { where: { mangopayCardId: mangopayCardId, isTemp: false } } },
  })
  const plan = await prisma.plan.findUnique({ where: { id: planId } })
  const adminUser = await prisma.user.findFirst({ where: { email: env.AUTH_ADMIN_EMAIL } })

  if (!user || !user.mangopayUserId) {
    logger.error("User or MangoPay User ID not found", { userId })
    throw new Error("Utilisateur ou compte MangoPay non trouvé.")
  }
  if (!plan) {
    logger.error("Plan not found", { planId })
    throw new Error("Plan d'abonnement non trouvé.")
  }

  // Check if plan offers free trial and user hasn't used it yet
  if (plan.freeTrialDays && !user.hasUsedFreeTrial) {
    logger.log("Creating free trial subscription", { userId, planId, freeTrialDays: plan.freeTrialDays })
    return await createFreeTrialSubscription({
      userId,
      planId,
      billingPeriod,
      browserInfos,
      IP,
      mangopayCardId,
      promotionalCodeId,
    })
  }

  const paymentCard = user.cards[0]

  if (!paymentCard) {
    logger.error("Specified MangoPay Card ID not found or doesn't belong to user", { userId, mangopayCardId })
    throw new Error("Carte de paiement spécifiée invalide ou non trouvée.")
  }
  if (!adminUser?.mangopayUserId || !adminUser?.mangopayWalletId) {
    logger.error("Admin user or MangoPay details not found", { adminEmail: env.AUTH_ADMIN_EMAIL })
    throw new Error("Configuration administrateur incomplète pour le paiement.")
  }

  // *** Check and Cancel Existing Registrations ***
  logger.log("Checking for existing recurring registrations for this card", { userId, mangopayCardId })

  const existingUserSubscriptions = await prisma.subscription.findMany({
    where: {
      userId: userId,
      mangopayRecurringRegistrationId: { not: null },
      status: { notIn: [SubscriptionStatus.CANCELED, SubscriptionStatus.EXPIRED] },
    },
    select: {
      id: true,
      mangopayRecurringRegistrationId: true,
    },
  })

  logger.warn(`Found ${existingUserSubscriptions.length} existing subscriptions for user ${userId}`)

  for (const existingSub of existingUserSubscriptions) {
    const existingRegId = existingSub.mangopayRecurringRegistrationId
    if (!existingRegId) continue

    try {
      logger.log(`Checking details for existing registration ${existingRegId}`, { linkedSubId: existingSub.id })
      const registrationDetails = await getRecurringRegistrationDetails(existingRegId)

      // Check if it's for the *same card* the user is trying now
      if (registrationDetails.CardId === paymentCard.mangopayCardId) {
        // Check if the registration is still potentially active
        if (["CREATED", "AUTHENTICATION_NEEDED", "IN_PROGRESS"].includes(registrationDetails.Status)) {
          logger.warn(
            `Found active/pending registration ${registrationDetails.Id} for card ${paymentCard.mangopayCardId} linked to sub ${existingSub.id}. Attempting to cancel it before creating a new one.`
          )
          await cancelRecurringPaymentRegistration(registrationDetails.Id)
          logger.log(`Successfully requested cancellation for existing registration ${registrationDetails.Id}`)

          // Update the old subscription status in DB immediately
          await prisma.subscription
            .update({
              where: { id: existingSub.id },
              data: {
                status: SubscriptionStatus.CANCELED,
                canceledAt: new Date(),
              },
            })
            .catch((err) =>
              logger.error(`Failed to update status of superseded subscription ${existingSub.id}`, { error: err })
            )
        } else {
          logger.log(
            `Found registration ${registrationDetails.Id} for card ${paymentCard.mangopayCardId} but its status is ${registrationDetails.Status}. No cancellation needed.`
          )
        }
      } else {
        logger.log(
          `Existing registration ${existingRegId} is for a different card (${registrationDetails.CardId}). Skipping cancellation.`
        )
      }
    } catch (error: unknown) {
      // @ts-expect-error unknown error
      logger.error(`Error processing existing registration ${existingRegId}: ${error.message}`, {
        registrationId: existingRegId,
        linkedSubId: existingSub.id,
      })
      throw new Error("Erreur lors de l'enregistrement de votre abonnement")
    }
  }
  // *** End Check and Cancel Logic ***

  // 2. Calculate payment details
  const amountInEuros = billingPeriod === BillingPeriod.MONTHLY ? plan.monthlyPrice / 100 : plan.annualPrice / 100
  const amountInCents = Math.round(amountInEuros * 100)
  const currency = "EUR"

  // 3. Calculate subscription dates
  const startDate = new Date()
  const endDate = billingPeriod === BillingPeriod.MONTHLY ? addMonths(startDate, 1) : addYears(startDate, 1)

  // 4. Create initial Prisma records
  let subscription
  let initialPayment
  try {
    const result = await prisma.$transaction(async (tx) => {
      const sub = await tx.subscription.create({
        data: {
          userId,
          planId,
          status: SubscriptionStatus.PENDING,
          billingPeriod,
          startDate,
          endDate,
          promotionalCodeId,
        },
      })
      logger.log("Initial subscription record created", { subscriptionId: sub.id })

      const payment = await tx.payment.create({
        data: {
          subscriptionId: sub.id,
          amount: amountInEuros,
          currency,
          status: PaymentStatus.PENDING,
        },
      })
      logger.log("Initial payment record created", { paymentId: payment.id })
      return { subscription: sub, initialPayment: payment }
    })
    subscription = result.subscription
    initialPayment = result.initialPayment
  } catch (dbError) {
    logger.error("Error creating initial Prisma records transaction", { userId, planId, error: dbError })
    throw new Error("Erreur lors de l'initialisation de l'abonnement en base de données.")
  }

  let mangopayUser: MangopayUser | null = null
  try {
    mangopayUser = await getMangoPayUser(user.mangopayUserId)
  } catch (error) {
    logger.error("Error retrieving mangopay user", { error, mangopayUserId: user.mangopayUserId })
    throw new Error("Erreur lors de la récupération des informations de paiement de l'utilisateur")
  }

  // Début de l'interaction MangoPay
  let registrationResultId: string | null = null
  let paymentResult: MangopayRecurringPayIn | null = null

  try {
    // 5. Create recurring payment registration
    const registrationResult = await createRecurringPayment({
      authorId: user.mangopayUserId,
      creditedUserId: adminUser.mangopayUserId,
      creditedWalletId: adminUser.mangopayWalletId,
      cardId: paymentCard.mangopayCardId!,
      firstTransactionAmount: amountInCents,
      nextTransactionAmount: amountInCents,
      currency,
      frequency: billingPeriod === BillingPeriod.MONTHLY ? "Monthly" : "Annual",
      tag: `CoheadCoaching Subscription Plan: ${plan.name} (${billingPeriod}), User: ${userId}, SubID: ${subscription.id}`,
      billing: mangopayUser?.Address
        ? {
            address: {
              AddressLine1: mangopayUser.Address.AddressLine1,
              City: mangopayUser.Address.City,
              PostalCode: mangopayUser.Address.PostalCode,
              Country: mangopayUser.Address.Country,
              Region: mangopayUser.Address.Region,
              AddressLine2: mangopayUser.Address.AddressLine2,
            },
            firstName: mangopayUser.FirstName,
            lastName: mangopayUser.LastName,
          }
        : undefined,
    })
    registrationResultId = registrationResult.Id
    logger.log("Recurring registration created", { registrationId: registrationResultId })

    // 6. Initiate the first payment
    const secureModeReturnURL = `${env.NEXT_PUBLIC_BASE_URL}/subscription/return?subscriptionId=${subscription.id}&paymentId=${initialPayment.id}`

    paymentResult = await initiateRecurringPayment({
      recurringPayinRegistrationId: registrationResult.Id,
      tag: `CoheadCoaching Sub ${subscription.id} - Initial Payment`,
      amount: amountInCents,
      currency,
      secureModeReturnURL,
      statementDescriptor: `ABO ${plan.name.substring(0, 6)}`,
      browserInfo: browserInfos,
      ipAddress: IP,
    })
    logger.log("Initial payment initiated", { payinId: paymentResult.Id, status: paymentResult.Status })

    // 7. Update Prisma records with MangoPay IDs and check payment status
    const paymentStatus =
      paymentResult.Status === "SUCCEEDED"
        ? PaymentStatus.SUCCEEDED
        : paymentResult.Status === "FAILED"
          ? PaymentStatus.FAILED
          : PaymentStatus.PENDING

    // Update subscription and payment status
    await prisma.$transaction([
      prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          mangopayRecurringRegistrationId: registrationResultId,
          status: paymentStatus === PaymentStatus.SUCCEEDED ? SubscriptionStatus.ACTIVE : SubscriptionStatus.PENDING,
        },
      }),
      prisma.payment.update({
        where: { id: initialPayment.id },
        data: {
          mangopayPayinId: paymentResult.Id,
          status: paymentStatus,
          failureReason: paymentResult.ResultMessage || null,
        },
      }),
    ])

    // Record promotional code usage if applicable and payment succeeded
    if (promotionalCodeId && paymentStatus === PaymentStatus.SUCCEEDED) {
      await prisma.$transaction([
        prisma.promotionalCodeUsage.create({
          data: {
            userId,
            promotionalCodeId,
            subscriptionId: subscription.id,
          },
        }),
        prisma.promotionalCode.update({
          where: { id: promotionalCodeId },
          data: {
            currentUsageCount: {
              increment: 1,
            },
          },
        }),
      ])
      logger.log("Promotional code usage recorded", { userId, promotionalCodeId, subscriptionId: subscription.id })
    }

    // 9. Return result for frontend handling
    // Récupérer les données à jour pour les renvoyer
    const finalSubscription = await prisma.subscription.findUnique({ where: { id: subscription.id } })
    const finalPayment = await prisma.payment.findUnique({ where: { id: initialPayment.id } })

    return {
      subscription: finalSubscription,
      payment: finalPayment,
      redirectUrl: paymentResult.SecureModeRedirectURL || null,
      returnUrl: paymentResult.SecureModeReturnURL,
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error("Error during MangoPay interaction or final DB update", {
      userId,
      subscriptionId: subscription.id,
      paymentId: initialPayment.id,
      registrationId: registrationResultId,
      payinId: paymentResult?.Id,
      error: errorMessage,
    })

    // Rollback
    try {
      await prisma.$transaction([
        prisma.payment.update({
          where: { id: initialPayment.id },
          data: {
            status: PaymentStatus.FAILED,
            mangopayPayinId: paymentResult?.Id || null,
            failureReason: `MangoPay Error: ${errorMessage.substring(0, 200)}`,
          },
        }),
        prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: SubscriptionStatus.FAILED,
            mangopayRecurringRegistrationId: registrationResultId || null,
          },
        }),
      ])
      logger.log("Subscription and Payment marked as FAILED after error", { subscriptionId: subscription.id })
    } catch (rollbackError) {
      logger.error("CRITICAL: Failed to rollback DB status after MangoPay error", {
        rollbackError,
        subscriptionId: subscription.id,
      })
    }

    if (errorMessage.includes("MangoPay") || errorMessage.includes("paiement") || errorMessage.includes("récurrent")) {
      throw new Error(
        "Une erreur est survenue lors de la communication avec le service de paiement. Veuillez vérifier vos informations ou réessayer plus tard."
      )
    }
    throw new Error("Une erreur technique est survenue lors de la création de votre abonnement.")
  }
}

export async function getMangopayPayInDetails(payInId: string) {
  return getPayInDetails(payInId)
}

export async function cancelRegistration(userId: string, subscriptionId?: string) {
  logger.log("Starting subscription cancellation", { userId, subscriptionId })

  // Find the subscription to cancel
  let subscription
  if (subscriptionId) {
    subscription = await prisma.subscription.findUnique({
      where: { id: subscriptionId },
      include: { plan: true, user: true, payments: true },
    })

    if (!subscription || subscription.userId !== userId) {
      throw new Error("Abonnement non trouvé ou non autorisé")
    }
  } else {
    // Find the user's active subscription
    subscription = await prisma.subscription.findFirst({
      where: {
        userId,
        status: { in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.PENDING] },
      },
      include: { plan: true, user: true, payments: true },
      orderBy: { createdAt: "desc" },
    })

    if (!subscription) {
      throw new Error("Aucun abonnement actif trouvé")
    }
  }

  // Check if subscription is already canceled
  if (subscription.status === SubscriptionStatus.CANCELED) {
    throw new Error("Cet abonnement est déjà annulé")
  }

  let refundResult = null
  let mangopayRegistrationCanceled = false

  try {
    // 1. Cancel MangoPay recurring registration if it exists
    if (subscription.mangopayRecurringRegistrationId) {
      try {
        await cancelRecurringPaymentRegistration(subscription.mangopayRecurringRegistrationId)
        mangopayRegistrationCanceled = true
        logger.log("MangoPay recurring registration canceled", {
          registrationId: subscription.mangopayRecurringRegistrationId,
        })
      } catch (error) {
        logger.error("Failed to cancel MangoPay recurring registration", {
          registrationId: subscription.mangopayRecurringRegistrationId,
          error: error instanceof Error ? error.message : String(error),
        })
        // Continue with cancellation even if MangoPay fails
      }
    }

    // 2. Update subscription status to canceled
    const updatedSubscription = await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        status: SubscriptionStatus.CANCELED,
        canceledAt: new Date(),
      },
    })

    logger.log("Subscription status updated to CANCELED", { subscriptionId: subscription.id })

    // 3. Check if plan has refund percentage for the subscription's billing period and process refund
    const appropriateRefundPercentage =
      subscription.billingPeriod === BillingPeriod.MONTHLY
        ? subscription.plan.monthlyRefundPercentage
        : subscription.plan.annualRefundPercentage

    if (appropriateRefundPercentage && appropriateRefundPercentage > 0) {
      logger.log("Plan has refund percentage for billing period, processing refund", {
        planId: subscription.plan.id,
        billingPeriod: subscription.billingPeriod,
        refundPercentage: appropriateRefundPercentage,
      })

      // Find the most recent successful payment to get the credited amount
      const lastSuccessfulPayment = subscription.payments.find(
        (payment) => payment.status === PaymentStatus.SUCCEEDED && payment.mangopayPayinId
      )

      if (!lastSuccessfulPayment?.mangopayPayinId) {
        logger.error("No successful payment found for refund calculation", {
          subscriptionId: subscription.id,
        })
        throw new Error("Aucun paiement réussi trouvé pour calculer le remboursement")
      }

      // Get MangoPay PayIn details to retrieve the credited amount (after fees)
      let creditedAmount: number
      try {
        const payinDetails = await getPayInDetails(lastSuccessfulPayment.mangopayPayinId)
        creditedAmount = payinDetails.CreditedFunds.Amount

        logger.log("Retrieved MangoPay PayIn details for refund calculation", {
          payinId: lastSuccessfulPayment.mangopayPayinId,
          debitedAmount: payinDetails.DebitedFunds.Amount,
          creditedAmount,
          fees: payinDetails.Fees.Amount,
        })
      } catch (error) {
        logger.error("Failed to retrieve MangoPay PayIn details", {
          payinId: lastSuccessfulPayment.mangopayPayinId,
          error: error instanceof Error ? error.message : String(error),
        })
        throw new Error("Impossible de récupérer les détails du paiement pour calculer le remboursement")
      }

      // Calculate refund amount based on percentage of credited amount
      const refundAmount = (creditedAmount * appropriateRefundPercentage) / 100

      logger.log("Calculated refund amount based on percentage", {
        creditedAmount,
        refundPercentage: appropriateRefundPercentage,
        refundAmount,
      })

      // Create refund record
      const refund = await prisma.refund.create({
        data: {
          userId: subscription.userId,
          planId: subscription.plan.id,
          subscriptionId: subscription.id,
          amount: refundAmount,
          status: RefundStatus.PENDING,
        },
      })

      // Try to process refund via MangoPay if we have payment details
      if (lastSuccessfulPayment?.mangopayPayinId && subscription.user.mangopayUserId) {
        try {
          const mangopayRefund = await refundPayin(
            lastSuccessfulPayment.mangopayPayinId,
            subscription.user.mangopayUserId,
            refundAmount
          )

          // Update refund record with success
          await prisma.refund.update({
            where: { id: refund.id },
            data: {
              status: RefundStatus.COMPLETED,
              mangopayRefundId: mangopayRefund.Id,
              processedAt: new Date(),
            },
          })

          refundResult = {
            id: refund.id,
            amount: refundAmount,
            status: "COMPLETED" as const,
            mangopayRefundId: mangopayRefund.Id,
          }

          logger.log("Refund processed successfully via MangoPay", {
            refundId: refund.id,
            mangopayRefundId: mangopayRefund.Id,
            amount: refundAmount,
          })
        } catch (refundError) {
          logger.error("Failed to process refund via MangoPay", {
            refundId: refund.id,
            error: refundError instanceof Error ? refundError.message : String(refundError),
          })

          // Update refund record with failure
          await prisma.refund.update({
            where: { id: refund.id },
            data: {
              status: RefundStatus.FAILED,
              failureReason: refundError instanceof Error ? refundError.message : "Erreur inconnue",
            },
          })

          refundResult = {
            id: refund.id,
            amount: refundAmount,
            status: "FAILED" as const,
            failureReason: refundError instanceof Error ? refundError.message : "Erreur inconnue",
          }
        }
      } else {
        // No payment details available for automatic refund
        refundResult = {
          id: refund.id,
          amount: refundAmount,
          status: "PENDING" as const,
          message: "Remboursement en attente de traitement manuel",
        }

        logger.log("Refund created but requires manual processing", {
          refundId: refund.id,
          reason: "No MangoPay payment details available",
        })
      }
    }

    return {
      subscription: updatedSubscription,
      refund: refundResult,
      mangopayRegistrationCanceled,
      message: refundResult
        ? "Abonnement annulé avec succès. Un remboursement a été traité."
        : "Abonnement annulé avec succès.",
    }
  } catch (error) {
    logger.error("Error during subscription cancellation", {
      userId,
      subscriptionId: subscription.id,
      error: error instanceof Error ? error.message : String(error),
    })

    // If we managed to cancel MangoPay but failed later, we should still mark as canceled
    if (mangopayRegistrationCanceled) {
      try {
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: SubscriptionStatus.CANCELED,
            canceledAt: new Date(),
          },
        })
        logger.log("Subscription marked as canceled despite later errors")
      } catch (dbError) {
        logger.error("Failed to update subscription status after MangoPay cancellation", {
          subscriptionId: subscription.id,
          dbError,
        })
      }
    }

    throw new Error(
      error instanceof Error ? error.message : "Une erreur est survenue lors de l'annulation de l'abonnement"
    )
  }
}
