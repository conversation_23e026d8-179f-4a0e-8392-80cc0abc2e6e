ENV=development
DATABASE_PRISMA_URL="postgresql://postgres:postgres@localhost:5432/postgres?schema=public"
DATABASE_URL_NON_POOLING="postgresql://postgres:postgres@localhost:5432/postgres?schema=public"
S3_REGION=fr-par
S3_ACCESS_KEY_ID=secret
S3_SECRET_ACCESS_KEY=secret
S3_ENDPOINT=s3.fr-par.scw.cloud
ENABLE_S3_SERVICE=false
MANGOPAY_PROXY_URL=https://freelance-and-me-backend-api-02256833d715.herokuapp.com
MANGOPAY_PROXY_AUTH_KEY=secret
NEXT_PUBLIC_BASE_URL=http://localhost:3000
EMAIL_WEBHOOK_URL=http://localhost:3000/api/webhooks/email/renewal-link
EMAIL_WEBHOOK_API_KEY=secure-api-key-here