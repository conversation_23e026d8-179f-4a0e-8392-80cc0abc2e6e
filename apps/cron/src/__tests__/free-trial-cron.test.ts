import { describe, expect, it, beforeEach, afterEach, jest } from "@jest/globals"
import { addDays } from "date-fns"

import { logger } from "@coheadcoaching/lib"
import { BillingPeriod, SubscriptionStatus } from "@prisma/client"

import { getExpiredTrials, getTrialsNeedingReminders } from "../lib/free-trial"
import { prisma } from "../lib/prisma"

// Mock the logger to avoid console output during tests
jest.mock("@coheadcoaching/lib", () => ({
  logger: {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}))

// Mock the email webhook to avoid actual email sending during tests
jest.mock("../lib/email-webhook", () => ({
  sendFreeTrialReminderEmailViaWebhook: jest.fn().mockResolvedValue({ success: true }),
}))

// Mock the mangopay functions to avoid actual payment processing during tests
jest.mock("../lib/mangopay", () => ({
  processSubsequentRecurringPayment: jest.fn().mockResolvedValue({
    Id: "test-payment-id",
    Status: "SUCCEEDED",
  }),
}))

// Mock data
const mockUser = {
  email: "<EMAIL>",
  username: "testuser",
  hasUsedFreeTrial: true,
  mangopayUserId: "test-mangopay-user",
  mangopayWalletId: "test-mangopay-wallet",
}

const mockPlan = {
  name: "Test Plan",
  description: "Test plan for cron jobs",
  monthlyPrice: 1000,
  annualPrice: 10000,
  freeTrialDays: 7,
  features: ["Feature 1"],
  isRecommended: false,
  isActive: true,
}

describe("Free Trial Cron Jobs", () => {
  let testUser: any
  let testPlan: any

  beforeEach(async () => {
    // Create test user
    testUser = await prisma.user.create({
      data: mockUser,
    })

    // Create test plan
    testPlan = await prisma.plan.create({
      data: mockPlan,
    })
  })

  afterEach(async () => {
    // Clean up test data
    await prisma.subscription.deleteMany({
      where: { userId: testUser.id },
    })
    await prisma.payment.deleteMany({
      where: { 
        subscription: { userId: testUser.id }
      },
    })
    await prisma.user.delete({
      where: { id: testUser.id },
    })
    await prisma.plan.delete({
      where: { id: testPlan.id },
    })

    // Clear all mocks
    jest.clearAllMocks()
  })

  describe("getTrialsNeedingReminders", () => {
    it("should find trials that need reminder emails", async () => {
      const now = new Date()
      const trialEndDate = addDays(now, 1) // Ends tomorrow, should trigger reminder

      const subscription = await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate: addDays(now, -6),
          endDate: addDays(trialEndDate, 30),
          isFreeTrial: true,
          freeTrialEndDate: trialEndDate,
          trialReminderSent: false,
        },
      })

      const trialsNeedingReminders = await getTrialsNeedingReminders()
      
      expect(trialsNeedingReminders).toHaveLength(1)
      expect(trialsNeedingReminders[0].id).toBe(subscription.id)
      expect(trialsNeedingReminders[0].user.email).toBe(testUser.email)
    })

    it("should not find trials that already had reminders sent", async () => {
      const now = new Date()
      const trialEndDate = addDays(now, 1)

      await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate: addDays(now, -6),
          endDate: addDays(trialEndDate, 30),
          isFreeTrial: true,
          freeTrialEndDate: trialEndDate,
          trialReminderSent: true, // Already sent
        },
      })

      const trialsNeedingReminders = await getTrialsNeedingReminders()
      
      expect(trialsNeedingReminders).toHaveLength(0)
    })

    it("should not find trials that are too far in the future", async () => {
      const now = new Date()
      const trialEndDate = addDays(now, 10) // Too far in the future

      await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate: addDays(now, -6),
          endDate: addDays(trialEndDate, 30),
          isFreeTrial: true,
          freeTrialEndDate: trialEndDate,
          trialReminderSent: false,
        },
      })

      const trialsNeedingReminders = await getTrialsNeedingReminders()
      
      expect(trialsNeedingReminders).toHaveLength(0)
    })
  })

  describe("getExpiredTrials", () => {
    it("should find expired free trials", async () => {
      const now = new Date()
      const expiredTrialEndDate = addDays(now, -1) // Expired yesterday

      const subscription = await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate: addDays(now, -8),
          endDate: addDays(now, 22),
          isFreeTrial: true,
          freeTrialEndDate: expiredTrialEndDate,
          trialReminderSent: true,
          mangopayRecurringRegistrationId: "test-registration-id",
        },
      })

      const expiredTrials = await getExpiredTrials()
      
      expect(expiredTrials).toHaveLength(1)
      expect(expiredTrials[0].id).toBe(subscription.id)
      expect(expiredTrials[0].freeTrialEndDate).toEqual(expiredTrialEndDate)
    })

    it("should not find trials that haven't expired yet", async () => {
      const now = new Date()
      const futureTrialEndDate = addDays(now, 1) // Expires tomorrow

      await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate: addDays(now, -6),
          endDate: addDays(futureTrialEndDate, 30),
          isFreeTrial: true,
          freeTrialEndDate: futureTrialEndDate,
          trialReminderSent: false,
        },
      })

      const expiredTrials = await getExpiredTrials()
      
      expect(expiredTrials).toHaveLength(0)
    })

    it("should not find non-trial subscriptions", async () => {
      const now = new Date()

      await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate: now,
          endDate: addDays(now, 30),
          isFreeTrial: false, // Not a trial
          freeTrialEndDate: null,
          trialReminderSent: false,
        },
      })

      const expiredTrials = await getExpiredTrials()
      
      expect(expiredTrials).toHaveLength(0)
    })
  })

  describe("Database Relationships", () => {
    it("should include user and plan data in trial queries", async () => {
      const now = new Date()
      const trialEndDate = addDays(now, 1)

      await prisma.subscription.create({
        data: {
          userId: testUser.id,
          planId: testPlan.id,
          status: SubscriptionStatus.ACTIVE,
          billingPeriod: BillingPeriod.MONTHLY,
          startDate: addDays(now, -6),
          endDate: addDays(trialEndDate, 30),
          isFreeTrial: true,
          freeTrialEndDate: trialEndDate,
          trialReminderSent: false,
        },
      })

      const trialsNeedingReminders = await getTrialsNeedingReminders()
      
      expect(trialsNeedingReminders).toHaveLength(1)
      expect(trialsNeedingReminders[0].user).toBeDefined()
      expect(trialsNeedingReminders[0].user.email).toBe(testUser.email)
      expect(trialsNeedingReminders[0].plan).toBeDefined()
      expect(trialsNeedingReminders[0].plan.name).toBe(testPlan.name)
    })
  })
})
