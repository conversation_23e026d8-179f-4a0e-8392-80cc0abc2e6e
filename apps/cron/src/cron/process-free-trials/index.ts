#!/usr/bin/env node

import chalk from "chalk"

import { logger } from "@coheadcoaching/lib"

import { processFreeTrials } from "../../lib/free-trial"

async function main() {
  try {
    console.log(chalk.blue("🔄 Starting free trial processing..."))

    await processFreeTrials()

    console.log(chalk.green("✅ Free trial processing completed successfully"))
    process.exit(0)
  } catch (error) {
    console.error(chalk.red("❌ Free trial processing failed:"))
    console.error(error)
    logger.error("Free trial processing failed", error)
    process.exit(1)
  }
}

main()
