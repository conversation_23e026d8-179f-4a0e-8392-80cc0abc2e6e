import { createHmac } from "crypto"

import { logger } from "@coheadcoaching/lib"
import { BillingPeriod } from "@prisma/client"

import { env } from "./env"

interface RenewalLinkEmailRequest {
  userEmail: string
  userName: string
  userLocale?: string
  token: string
  subscription: {
    id: string
    plan: {
      name: string
    }
    billingPeriod: BillingPeriod
  }
  expiresAt: string // ISO string
  timestamp: number
}

interface FreeTrialReminderEmailRequest {
  to: string
  userName: string
  planName: string
  trialEndDate: string
  manageSubscriptionUrl: string
  logoUrl: string
  timestamp: number
}

interface WebhookResponse {
  success: boolean
  error?: string
  reason?: string
}

function createSignature(payload: string, secret: string): string {
  return "sha256=" + createHmac("sha256", secret).update(payload).digest("hex")
}

async function sendWebhookRequest({
  url,
  payload,
  apiKey,
  secret,
  retries = 3,
}: {
  url: string
  payload: RenewalLinkEmailRequest | FreeTrialReminderEmailRequest
  apiKey: string
  secret: string
  retries?: number
}): Promise<WebhookResponse> {
  const payloadString = JSON.stringify(payload)
  const signature = createSignature(payloadString, secret)

  const headers = {
    "Content-Type": "application/json",
    "X-API-Key": apiKey,
    "X-Signature": signature,
    "User-Agent": "CoheadCoaching-Cron/1.0",
  }

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      logger.log(`Sending email webhook request (attempt ${attempt}/${retries})`)

      const response = await fetch(url, {
        method: "POST",
        headers,
        body: payloadString,
        // Add timeout
        signal: AbortSignal.timeout(30000), // 30 seconds timeout
      })

      if (!response.ok) {
        const errorText = await response.text()
        logger.warn(`Webhook request failed with status ${response.status}: ${errorText}`)

        // Don't retry for client errors (4xx), only server errors (5xx)
        if (response.status >= 400 && response.status < 500) {
          return {
            success: false,
            error: `Client error: ${response.status}`,
            reason: errorText,
          }
        }

        // For server errors, continue to retry
        if (attempt === retries) {
          return {
            success: false,
            error: `Server error after ${retries} attempts: ${response.status}`,
            reason: errorText,
          }
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000) // Max 10 seconds
        logger.log(`Retrying in ${delay}ms...`)
        await new Promise((resolve) => setTimeout(resolve, delay))
        continue
      }

      const result: WebhookResponse = await response.json()
      logger.log(`Webhook request successful`)
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      logger.warn(`Webhook request attempt ${attempt} failed: ${errorMessage}`)

      if (attempt === retries) {
        return {
          success: false,
          error: `Network error after ${retries} attempts`,
          reason: errorMessage,
        }
      }

      // Wait before retrying
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000)
      logger.log(`Retrying in ${delay}ms...`)
      await new Promise((resolve) => setTimeout(resolve, delay))
    }
  }

  // This should never be reached, but TypeScript requires it
  return {
    success: false,
    error: "Unexpected error",
    reason: "Failed to complete webhook request",
  }
}

export async function sendRenewalLinkEmailViaWebhook({
  userEmail,
  userName,
  token,
  subscription,
  expiresAt,
  userLocale,
}: {
  userEmail: string
  userName: string
  token: string
  subscription: {
    id: string
    plan: { name: string }
    billingPeriod: BillingPeriod
  }
  expiresAt: Date
  userLocale?: string
}): Promise<{ success: boolean; reason?: string }> {
  try {
    // Check if webhook is configured
    const webhookUrl = env.EMAIL_WEBHOOK_URL
    const apiKey = env.EMAIL_WEBHOOK_API_KEY

    if (!webhookUrl || !apiKey) {
      logger.error("Email webhook not configured. Missing EMAIL_WEBHOOK_URL or EMAIL_WEBHOOK_API_KEY")
      return {
        success: false,
        reason: "Email webhook not configured",
      }
    }

    // Use API key as secret if no separate secret is provided
    const secret = apiKey

    // Prepare webhook payload
    const payload: RenewalLinkEmailRequest = {
      userEmail,
      userName,
      userLocale,
      token,
      subscription,
      expiresAt: expiresAt.toISOString(),
      timestamp: Date.now(),
    }

    logger.log(`Sending renewal link email webhook for subscription ${subscription.id}`)
    logger.log(`- User: ${userEmail}`)
    logger.log(`- Webhook URL: ${webhookUrl}`)

    // Send webhook request
    const result = await sendWebhookRequest({ url: webhookUrl, payload, apiKey, secret })

    if (result.success) {
      logger.log(`Renewal link email webhook sent successfully for subscription ${subscription.id}`)
      return { success: true }
    } else {
      logger.error(`Renewal link email webhook failed: ${result.error} - ${result.reason}`)
      return {
        success: false,
        reason: result.reason || result.error || "Unknown webhook error",
      }
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Failed to send renewal link email webhook: ${errorMessage}`, { error })

    return {
      success: false,
      reason: errorMessage,
    }
  }
}

export async function sendFreeTrialReminderEmailViaWebhook({
  to,
  userName,
  planName,
  trialEndDate,
  manageSubscriptionUrl,
  logoUrl,
}: {
  to: string
  userName: string
  planName: string
  trialEndDate: string
  manageSubscriptionUrl: string
  logoUrl: string
}): Promise<{ success: boolean; reason?: string }> {
  try {
    if (!env.EMAIL_WEBHOOK_URL || !env.EMAIL_WEBHOOK_API_KEY || !env.EMAIL_WEBHOOK_SECRET) {
      logger.warn("Email webhook not configured, skipping free trial reminder email")
      return { success: false, reason: "Email webhook not configured" }
    }

    const payload: FreeTrialReminderEmailRequest = {
      to,
      userName,
      planName,
      trialEndDate,
      manageSubscriptionUrl,
      logoUrl,
      timestamp: Date.now(),
    }

    const result = await sendWebhookRequest({
      url: `${env.EMAIL_WEBHOOK_URL}/free-trial-reminder`,
      payload,
      apiKey: env.EMAIL_WEBHOOK_API_KEY,
      secret: env.EMAIL_WEBHOOK_SECRET,
    })

    if (result.success) {
      logger.log(`Free trial reminder email webhook sent successfully to ${to}`)
      return { success: true }
    } else {
      logger.error(`Free trial reminder email webhook failed: ${result.error} - ${result.reason}`)
      return {
        success: false,
        reason: result.reason || result.error || "Unknown webhook error",
      }
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Failed to send free trial reminder email webhook: ${errorMessage}`, { error })

    return {
      success: false,
      reason: errorMessage,
    }
  }
}
