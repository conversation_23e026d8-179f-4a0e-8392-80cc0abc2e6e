import { addDays, isBefore } from "date-fns"

import { logger } from "@coheadcoaching/lib"
import { BillingPeriod, SubscriptionStatus } from "@prisma/client"

import { sendFreeTrialReminderEmailViaWebhook } from "./email-webhook"
import { env } from "./env"
import { processSubsequentRecurringPayment } from "./mangopay"
import { prisma } from "./prisma"

const FREE_TRIAL_REMINDER_DAYS_BEFORE = 2 // Send reminder 2 days before trial ends

/**
 * Get free trial subscriptions that need reminder emails
 */
export async function getTrialsNeedingReminders() {
  const reminderDate = addDays(new Date(), FREE_TRIAL_REMINDER_DAYS_BEFORE)

  const subscriptions = await prisma.subscription.findMany({
    where: {
      isFreeTrial: true,
      trialReminderSent: false,
      freeTrialEndDate: {
        lte: reminderDate,
      },
      status: SubscriptionStatus.ACTIVE,
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          username: true,
          name: true,
        },
      },
      plan: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })

  return subscriptions
}

/**
 * Get expired free trial subscriptions that need to be processed
 */
export async function getExpiredTrials() {
  const now = new Date()

  const subscriptions = await prisma.subscription.findMany({
    where: {
      isFreeTrial: true,
      freeTrialEndDate: {
        lt: now,
      },
      status: SubscriptionStatus.ACTIVE,
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          username: true,
          name: true,
          mangopayUserId: true,
        },
      },
      plan: {
        select: {
          id: true,
          name: true,
          monthlyPrice: true,
          annualPrice: true,
        },
      },
    },
  })

  return subscriptions
}

/**
 * Send free trial reminder emails
 */
export async function sendFreeTrialReminders() {
  logger.log("Starting free trial reminder job")

  try {
    const subscriptions = await getTrialsNeedingReminders()
    
    logger.log(`Found ${subscriptions.length} trials needing reminders`)

    for (const subscription of subscriptions) {
      try {
        if (!subscription.user.email) {
          logger.warn("User has no email address", { userId: subscription.userId })
          continue
        }

        const userName = subscription.user.username || subscription.user.name || "Utilisateur"
        const trialEndDate = subscription.freeTrialEndDate!.toLocaleDateString("fr-FR", {
          day: "numeric",
          month: "long",
          year: "numeric",
        })

        // Send reminder email via webhook
        await sendFreeTrialReminderEmailViaWebhook({
          to: subscription.user.email,
          userName,
          planName: subscription.plan.name,
          trialEndDate,
          manageSubscriptionUrl: `${env.NEXT_PUBLIC_BASE_URL}/profile/subscription`,
          logoUrl: `${env.NEXT_PUBLIC_BASE_URL}/logo.svg`,
        })

        // Mark reminder as sent
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: { trialReminderSent: true },
        })

        logger.log("Free trial reminder sent", {
          subscriptionId: subscription.id,
          userEmail: subscription.user.email,
        })
      } catch (error) {
        logger.error("Failed to send free trial reminder", {
          subscriptionId: subscription.id,
          error,
        })
      }
    }

    logger.log("Free trial reminder job completed", { processed: subscriptions.length })
  } catch (error) {
    logger.error("Free trial reminder job failed", error)
    throw error
  }
}

/**
 * Process expired free trials by initiating the first real payment
 */
export async function processExpiredFreeTrials() {
  logger.log("Starting expired free trials processing job")

  try {
    const subscriptions = await getExpiredTrials()
    
    logger.log(`Found ${subscriptions.length} expired trials to process`)

    for (const subscription of subscriptions) {
      try {
        if (!subscription.mangopayRecurringRegistrationId) {
          logger.error("Subscription missing recurring registration ID", {
            subscriptionId: subscription.id,
          })
          continue
        }

        const amount = subscription.billingPeriod === BillingPeriod.MONTHLY 
          ? subscription.plan.monthlyPrice 
          : subscription.plan.annualPrice

        // Process the first real payment using the existing mangopay function
        const paymentResult = await processSubsequentRecurringPayment({
          recurringRegistrationId: subscription.mangopayRecurringRegistrationId,
          amount,
          currency: "EUR",
          tag: `CoheadCoaching Trial Expiration - Sub: ${subscription.id}`,
          statementDescriptor: `ABO ${subscription.plan.name.substring(0, 6)}`,
        })

        // Create payment record
        await prisma.payment.create({
          data: {
            subscriptionId: subscription.id,
            amount,
            currency: "EUR",
            status: paymentResult.Status === "SUCCEEDED" ? "SUCCEEDED" : "PENDING",
            mangopayPayinId: paymentResult.Id,
          },
        })

        // Update subscription to mark trial as completed
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            isFreeTrial: false,
            freeTrialEndDate: null,
          },
        })

        logger.log("Expired trial processed successfully", {
          subscriptionId: subscription.id,
          userId: subscription.userId,
          paymentStatus: paymentResult.Status,
        })
      } catch (error) {
        logger.error("Failed to process expired trial", {
          subscriptionId: subscription.id,
          error,
        })

        // Mark subscription as failed if payment fails
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: SubscriptionStatus.FAILED,
          },
        })
      }
    }

    logger.log("Expired free trials processing job completed", { processed: subscriptions.length })
  } catch (error) {
    logger.error("Expired free trials processing job failed", error)
    throw error
  }
}

/**
 * Combined job that runs both reminder and expiration processing
 */
export async function processFreeTrials() {
  logger.log("Starting combined free trial processing")

  try {
    // Run reminder job first
    await sendFreeTrialReminders()
    
    // Then process expirations
    await processExpiredFreeTrials()

    logger.log("Combined free trial processing completed successfully")
  } catch (error) {
    logger.error("Combined free trial processing failed", error)
    throw error
  }
}
