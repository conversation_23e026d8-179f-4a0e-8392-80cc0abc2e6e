import { randomBytes } from "crypto"
import { addDays, addMonths, addYears, startOfDay } from "date-fns"

import { logger } from "@coheadcoaching/lib"
import { BillingPeriod, Payment, PaymentStatus, RenewalLinkStatus, SubscriptionStatus } from "@prisma/client"

import { sendRenewalLinkEmailViaWebhook } from "./email-webhook"
import { env } from "./env"
import { getRecurringRegistrationDetails, processSubsequentRecurringPayment } from "./mangopay"
import { prisma } from "./prisma"

export async function processDueSubscriptions() {
  const today = startOfDay(new Date())
  logger.log(`Starting subscription renewal process for date: ${today.toISOString()}`)

  const dueSubscriptions = await prisma.subscription.findMany({
    where: {
      status: SubscriptionStatus.ACTIVE,
      endDate: {
        lte: today,
      },
      mangopayRecurringRegistrationId: {
        not: null,
      },
    },
    include: {
      user: {
        select: { id: true, mangopayUserId: true, email: true, name: true },
      },
      plan: true,
    },
  })

  logger.log(`Found ${dueSubscriptions.length} subscriptions due for renewal.`)

  for (const subscription of dueSubscriptions) {
    logger.log(`Processing renewal for subscription: ${subscription.id}, User: ${subscription.userId}`)

    // Skip admin-managed subscriptions - they should expire instead of auto-renew
    if (subscription.isAdminManaged) {
      logger.log(`Subscription ${subscription.id} is admin-managed. Setting status to EXPIRED instead of auto-renewal.`)
      await updateSubscriptionStatus(subscription.id, SubscriptionStatus.EXPIRED, "Abonnement géré par l'admin expiré.")
      continue
    }

    if (!subscription.mangopayRecurringRegistrationId || !subscription.user.mangopayUserId) {
      logger.error(`Subscription ${subscription.id} missing required Mangopay IDs. Skipping.`)
      await updateSubscriptionStatus(subscription.id, SubscriptionStatus.FAILED, "Configuration Mangopay manquante.")
      continue
    }

    try {
      const registrationDetails = await getRecurringRegistrationDetails(subscription.mangopayRecurringRegistrationId)
      if (registrationDetails.Status === "AUTHENTICATION_NEEDED") {
        logger.warn(
          `Subscription ${subscription.id} requires re-authentication (Registration Status: AUTHENTICATION_NEEDED). Creating renewal link for customer-initiated transaction.`
        )

        await updateSubscriptionStatus(
          subscription.id,
          SubscriptionStatus.AUTHENTICATION_NEEDED,
          "Re-authentification bancaire requise."
        )

        // Create renewal link for customer-initiated transaction
        try {
          const renewalLink = await createRenewalLink(subscription.id)
          logger.log(`Successfully created renewal link ${renewalLink.id} for subscription ${subscription.id}`)

          // Send email notification to user about renewal link
          if (subscription.user.email) {
            try {
              await sendRenewalLinkNotification(
                {
                  id: subscription.id,
                  user: {
                    email: subscription.user.email,
                    name: subscription.user.name,
                  },
                  plan: {
                    name: subscription.plan.name,
                  },
                  billingPeriod: subscription.billingPeriod,
                },
                renewalLink.token,
                renewalLink.expiresAt
              )
              logger.log(`Renewal link email sent successfully for subscription ${subscription.id}`)
            } catch (emailError) {
              logger.error(
                `Failed to send renewal link email for subscription ${subscription.id}: ${emailError instanceof Error ? emailError.message : String(emailError)}`,
                { emailError }
              )
              // Don't fail the entire process if email fails
            }
          } else {
            logger.warn(`Cannot send renewal link email for subscription ${subscription.id}: user has no email address`)
          }
        } catch (linkError) {
          logger.error(
            `Failed to create renewal link for subscription ${subscription.id}: ${linkError instanceof Error ? linkError.message : String(linkError)}`,
            { linkError }
          )
          // Continue processing other subscriptions even if link creation fails
        }
        continue
      } else if (registrationDetails.Status === "ENDED") {
        logger.warn(
          `Subscription ${subscription.id} linked to an ENDED/FAILED registration (${registrationDetails.Status}). Skipping renewal. Status set to CANCELED/FAILED.`
        )
        const finalStatus =
          registrationDetails.Status === "ENDED" ? SubscriptionStatus.CANCELED : SubscriptionStatus.FAILED
        await updateSubscriptionStatus(
          subscription.id,
          finalStatus,
          `Enregistrement Mangopay terminé ou échoué (${registrationDetails.Status}).`
        )
        continue
      } else if (registrationDetails.Status !== "IN_PROGRESS" && registrationDetails.Status !== "CREATED") {
        logger.warn(
          `Subscription ${subscription.id} linked to registration with unexpected status: ${registrationDetails.Status}. Skipping renewal.`
        )
        await updateSubscriptionStatus(
          subscription.id,
          SubscriptionStatus.FAILED,
          `Statut Mangopay inattendu: ${registrationDetails.Status}.`
        )
        continue
      }
    } catch (error) {
      logger.error(
        `Failed to get registration details for ${subscription.mangopayRecurringRegistrationId} during renewal check for sub ${subscription.id}: ${error instanceof Error ? error.message : String(error)}`,
        { error }
      )
      await updateSubscriptionStatus(subscription.id, SubscriptionStatus.FAILED, "Erreur vérification Mangopay.")
      continue
    }

    const amountInEuros =
      subscription.billingPeriod === BillingPeriod.MONTHLY
        ? subscription.plan.monthlyPrice / 100
        : subscription.plan.annualPrice / 100
    const amountInCents = Math.round(amountInEuros * 100)
    const currency = "EUR"

    let paymentRecord: Payment | null = null

    try {
      paymentRecord = await prisma.payment.create({
        data: {
          subscriptionId: subscription.id,
          amount: amountInEuros,
          currency: currency,
          status: PaymentStatus.PENDING,
        },
      })
      logger.log(`Created pending payment record ${paymentRecord.id} for subscription ${subscription.id}`)

      const mitResult = await processSubsequentRecurringPayment({
        recurringPayinRegistrationId: subscription.mangopayRecurringRegistrationId,
        amount: amountInCents,
        currency: currency,
        tag: `CoheadCoaching Renewal - Sub: ${subscription.id}, Payment: ${paymentRecord.id}`,
        statementDescriptor: `ABO ${subscription.plan.name.substring(0, 6)}`,
      })

      if (mitResult.Status === "SUCCEEDED") {
        logger.log(`MIT Payment SUCCEEDED for subscription ${subscription.id}. PayIn ID: ${mitResult.Id}`)
        await prisma.payment.update({
          where: { id: paymentRecord.id },
          data: {
            status: PaymentStatus.SUCCEEDED,
            mangopayPayinId: mitResult.Id,
            failureReason: null,
          },
        })

        const newEndDate =
          subscription.billingPeriod === BillingPeriod.MONTHLY
            ? addMonths(subscription.endDate, 1)
            : addYears(subscription.endDate, 1)

        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            status: SubscriptionStatus.ACTIVE,
            endDate: newEndDate,
          },
        })
        logger.log(`Subscription ${subscription.id} renewed. New end date: ${newEndDate.toISOString()}`)
      } else {
        logger.error(
          `MIT Payment FAILED for subscription ${subscription.id}. Status: ${mitResult.Status}, Reason: ${mitResult.ResultMessage}`
        )
        await prisma.payment.update({
          where: { id: paymentRecord.id },
          data: {
            status: PaymentStatus.FAILED,
            mangopayPayinId: mitResult.Id,
            failureReason: mitResult.ResultMessage || "Paiement récurrent échoué",
          },
        })

        await updateSubscriptionStatus(
          subscription.id,
          SubscriptionStatus.FAILED,
          `Échec du paiement récurrent : ${mitResult.ResultMessage}`
        )
        // TODO: Notify user about payment failure
      }
    } catch (error) {
      logger.error(
        `Error processing renewal for subscription ${subscription.id}: ${error instanceof Error ? error.message : String(error)}`,
        { error }
      )

      if (paymentRecord) {
        await prisma.payment.update({
          where: { id: paymentRecord.id },
          data: {
            status: PaymentStatus.FAILED,
            failureReason: `Erreur système lors du renouvellement : ${error instanceof Error ? error.message : String(error)}`,
          },
        })
      }
      await updateSubscriptionStatus(
        subscription.id,
        SubscriptionStatus.FAILED,
        `Erreur système lors du renouvellement.`
      )
      // TODO: Notify admin/monitor this error
    }
  }

  logger.log("Finished subscription renewal process.")
}

async function updateSubscriptionStatus(subscriptionId: string, status: SubscriptionStatus, logReason: string) {
  try {
    await prisma.subscription.update({
      where: { id: subscriptionId },
      data: { status: status, ...(status === SubscriptionStatus.CANCELED && { canceledAt: new Date() }) },
    })
    logger.log(`Subscription ${subscriptionId} status updated to ${status}. Reason: ${logReason}`)
  } catch (dbError) {
    logger.error(
      `Failed to update subscription ${subscriptionId} status to ${status} in DB: ${dbError instanceof Error ? dbError.message : String(dbError)}`,
      {
        dbError,
      }
    )
  }
}

async function createRenewalLink(subscriptionId: string) {
  try {
    logger.log(`Creating renewal link for subscription ${subscriptionId}`)

    // Verify subscription exists and get user details
    const subscription = await prisma.subscription.findUnique({
      where: { id: subscriptionId },
      include: {
        user: {
          select: { id: true, email: true, name: true },
        },
        plan: {
          select: { id: true, name: true },
        },
      },
    })

    if (!subscription) {
      throw new Error(`Subscription ${subscriptionId} not found`)
    }

    if (!subscription.user.email) {
      throw new Error(`User for subscription ${subscriptionId} has no email address`)
    }

    // Generate a secure random token
    const token = randomBytes(32).toString("hex")

    // Set expiration to 30 days from now
    const expiresAt = addDays(new Date(), 30)

    // Check if there's already a pending renewal link for this subscription
    const existingLink = await prisma.renewalLink.findFirst({
      where: {
        subscriptionId,
        status: RenewalLinkStatus.PENDING,
        expiresAt: {
          gt: new Date(),
        },
      },
    })

    if (existingLink) {
      logger.log(`Active renewal link already exists for subscription ${subscriptionId}: ${existingLink.id}`)
      return existingLink
    }

    // Create new renewal link
    const renewalLink = await prisma.renewalLink.create({
      data: {
        token,
        subscriptionId,
        status: RenewalLinkStatus.PENDING,
        expiresAt,
      },
    })

    logger.log(
      `Successfully created renewal link ${renewalLink.id} for subscription ${subscriptionId}. Expires: ${expiresAt.toISOString()}`
    )

    return renewalLink
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Failed to create renewal link for subscription ${subscriptionId}: ${errorMessage}`, { error })
    throw new Error(`Renewal link creation failed: ${errorMessage}`)
  }
}

async function sendRenewalLinkNotification(
  subscription: {
    id: string
    user: { email: string; name: string | null }
    plan: { name: string }
    billingPeriod: BillingPeriod
  },
  token: string,
  expiresAt: Date
) {
  try {
    const baseUrl = env.NEXT_PUBLIC_BASE_URL
    const renewalUrl = `${baseUrl}/subscription/renew/${token}`

    logger.log(`Sending renewal link notification:`)
    logger.log(`- User: ${subscription.user.email} (${subscription.user.name || "Unknown"})`)
    logger.log(`- Plan: ${subscription.plan.name}`)
    logger.log(`- Billing: ${subscription.billingPeriod}`)
    logger.log(`- Renewal URL: ${renewalUrl}`)
    logger.log(`- Expires: ${expiresAt.toISOString()}`)

    // Send email via webhook to the main app
    const result = await sendRenewalLinkEmailViaWebhook({
      userEmail: subscription.user.email,
      userName: subscription.user.name || "User",
      token: token,
      subscription: {
        id: subscription.id,
        plan: { name: subscription.plan.name },
        billingPeriod: subscription.billingPeriod,
      },
      expiresAt,
    })

    if (result.success) {
      logger.log(`Renewal link email sent successfully via webhook for subscription ${subscription.id}`)
      return { success: true }
    } else {
      logger.error(`Failed to send renewal link email via webhook: ${result.reason}`)
      throw new Error(`Email notification failed: ${result.reason}`)
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logger.error(`Failed to send renewal link notification: ${errorMessage}`, { error })
    throw new Error(`Email notification failed: ${errorMessage}`)
  }
}
