import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components"
import * as React from "react"

interface AccountRecoveryEmailProps {
  userName: string
  recoveryUrl: string
  gracePeriodDays: number | null
  logoUrl: string
}

export const AccountRecoveryEmail = ({
  userName = "Utilisateur",
  recoveryUrl = "https://coheadcoaching.com/account-recovery",
  gracePeriodDays = 30,
  logoUrl = "https://coheadcoaching.com/logo.svg",
}: AccountRecoveryEmailProps) => {
  const previewText = `Récupérez votre compte CoheadCoaching`

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img src={logoUrl} width="120" height="36" alt="CoheadCoaching" style={logo} />
          </Section>

          <Heading style={heading}>Récupération de votre compte</Heading>

          <Text style={paragraph}>Bonjour {userName},</Text>

          <Text style={paragraph}>
            Votre compte CoheadCoaching a été temporairement gelé suite à une demande de suppression.
          </Text>

          <Text style={paragraph}>
            Bonne nouvelle ! Vous pouvez récupérer votre compte et toutes vos données en cliquant sur le bouton ci-dessous.
          </Text>

          {gracePeriodDays && (
            <Text style={paragraph}>
              <strong>Important :</strong> Vous avez {gracePeriodDays} jours pour récupérer votre compte. 
              Après cette période, votre compte sera définitivement supprimé.
            </Text>
          )}

          <Section style={buttonContainer}>
            <Button style={button} href={recoveryUrl}>
              Récupérer mon compte
            </Button>
          </Section>

          <Text style={paragraph}>
            Si vous ne souhaitez pas récupérer votre compte, vous pouvez ignorer cet email. 
            {gracePeriodDays 
              ? `Votre compte sera automatiquement supprimé après ${gracePeriodDays} jours.`
              : "Votre compte restera gelé jusqu'à ce que vous le récupériez."
            }
          </Text>

          <Hr style={hr} />

          <Text style={footer}>
            Vous recevez cet email car une demande de suppression de compte a été effectuée.
            <br />
            Si vous n'êtes pas à l'origine de cette demande, veuillez contacter notre support immédiatement.
          </Text>
        </Container>
      </Body>
    </Html>
  )
}

export default AccountRecoveryEmail

const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "560px",
}

const logoContainer = {
  textAlign: "center" as const,
  margin: "0 0 40px",
}

const logo = {
  margin: "0 auto",
}

const heading = {
  fontSize: "24px",
  letterSpacing: "-0.5px",
  lineHeight: "1.3",
  fontWeight: "400",
  color: "#484848",
  padding: "17px 0 0",
  textAlign: "center" as const,
}

const paragraph = {
  margin: "0 0 15px",
  fontSize: "15px",
  lineHeight: "1.4",
  color: "#3c4149",
}

const buttonContainer = {
  textAlign: "center" as const,
  margin: "32px 0",
}

const button = {
  backgroundColor: "#5469d4",
  borderRadius: "6px",
  fontWeight: "600",
  color: "#fff",
  fontSize: "15px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  padding: "12px 24px",
}

const hr = {
  borderColor: "#cccccc",
  margin: "20px 0",
}

const footer = {
  color: "#8898aa",
  fontSize: "12px",
  textAlign: "center" as const,
}

const link = {
  color: "#5469d4",
  textDecoration: "underline",
}
