import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components"
import * as React from "react"

interface FreeTrialReminderEmailProps {
  userName: string
  planName: string
  trialEndDate: string
  manageSubscriptionUrl: string
  logoUrl: string
}

export const FreeTrialReminderEmail = ({
  userName = "Utilisateur",
  planName = "Plan Premium",
  trialEndDate = "15 janvier 2024",
  manageSubscriptionUrl = "https://coheadcoaching.com/profile/subscription",
  logoUrl = "https://coheadcoaching.com/logo.svg",
}: FreeTrialReminderEmailProps) => {
  const previewText = `Votre essai gratuit ${planName} se termine bientôt`

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img src={logoUrl} width="120" height="36" alt="CoheadCoaching" style={logo} />
          </Section>

          <Heading style={heading}>Votre essai gratuit se termine bientôt</Heading>

          <Text style={paragraph}>Bonjour {userName},</Text>

          <Text style={paragraph}>
            Nous espérons que vous appréciez votre essai gratuit du plan <strong>{planName}</strong> !
          </Text>

          <Text style={paragraph}>
            Votre période d'essai gratuit se terminera le <strong>{trialEndDate}</strong>. 
            Après cette date, votre abonnement sera automatiquement activé et votre méthode de paiement sera débitée.
          </Text>

          <Section style={buttonContainer}>
            <Button style={button} href={manageSubscriptionUrl}>
              Gérer mon abonnement
            </Button>
          </Section>

          <Text style={paragraph}>
            Si vous souhaitez annuler votre abonnement avant la fin de la période d'essai, 
            vous pouvez le faire à tout moment depuis votre espace personnel.
          </Text>

          <Hr style={hr} />

          <Text style={footer}>
            Vous recevez cet email car vous avez un essai gratuit actif sur CoheadCoaching.
            <br />
            <Link href={manageSubscriptionUrl} style={link}>
              Gérer mes préférences d'email
            </Link>
          </Text>
        </Container>
      </Body>
    </Html>
  )
}

export default FreeTrialReminderEmail

const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "560px",
}

const logoContainer = {
  textAlign: "center" as const,
  margin: "0 0 40px",
}

const logo = {
  margin: "0 auto",
}

const heading = {
  fontSize: "24px",
  letterSpacing: "-0.5px",
  lineHeight: "1.3",
  fontWeight: "400",
  color: "#484848",
  padding: "17px 0 0",
  textAlign: "center" as const,
}

const paragraph = {
  margin: "0 0 15px",
  fontSize: "15px",
  lineHeight: "1.4",
  color: "#3c4149",
}

const buttonContainer = {
  textAlign: "center" as const,
  margin: "32px 0",
}

const button = {
  backgroundColor: "#5469d4",
  borderRadius: "6px",
  fontWeight: "600",
  color: "#fff",
  fontSize: "15px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  padding: "12px 24px",
}

const hr = {
  borderColor: "#cccccc",
  margin: "20px 0",
}

const footer = {
  color: "#8898aa",
  fontSize: "12px",
  textAlign: "center" as const,
}

const link = {
  color: "#5469d4",
  textDecoration: "underline",
}
